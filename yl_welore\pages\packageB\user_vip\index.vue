<template>
	<view class="page-container">
		<cu-custom bgColor="none" :isSearch="false" :isBack="true">
			<view slot="backText">返回</view>
			<view slot="content" style="color: #000000;font-weight: 600; font-size: 36rpx;">👑 会员中心</view>
		</cu-custom>
		<view class="member-card">
			<view class="member-info-container">
				<view class="user-info-section">
					<view class="user-avatar-wrapper">
						<view class="cu-avatar round lg member-avatar"
							:style="'background-image:url('+(user_info.user_head_sculpture)+');'"></view>
					</view>
					<view class="user-details">
						<view class="username-row">
							<text class="username">{{user_info.user_nick_name}}</text>
							<image class="vip-badge" mode="widthFix" v-if="user_info.is_vip==1"
								:src="(http_root)+'addons/yl_welore/web/static/applet_icon/vip.png'"></image>
							<image class="vip-badge" mode="widthFix" v-if="user_info.is_vip==0"
								:src="(http_root)+'addons/yl_welore/web/static/applet_icon/novip.png'"></image>
						</view>
						<view class="member-status">
							💎 {{user_info.vip_end_time_tmpl==0?'您还不是会员！':user_info.vip_end_time_tmpl}}
						</view>
					</view>
				</view>
				<view class="balance-section">
					<view class="balance-label">💰 我的余额</view>
					<view class="balance-info">
						<image :src="$state.diy.currency_icon" class="currency-icon"></image>
						<text class="balance-amount">{{user_info.conch}}</text>
					</view>

				</view>
				<view class="recharge-section">
					<button v-if="copyright.recharge_arbor==1&&chenck_phone" @tap="get_pay" class="recharge-btn">
						充值
					</button>
					<button v-if="copyright.recharge_arbor==1&&chenck_phone==false" open-type="getPhoneNumber"
						class="recharge-btn">
						充值
					</button>
				</view>
			</view>
		</view>

		<view v-if="user_honorary.chop_type==0" class="membership-packages">
			<view class="section-title">
				<text class="title-text">🎁 会员套餐</text>
				<text class="subtitle-text">选择适合您的会员套餐</text>
			</view>

			<scroll-view :scroll-x="true" class="packages-scroll">
				<view :class="'package-card ' + (pay_index==p_index ? 'package-selected' : '')" @tap="get_pay_index"
					:data-index="p_index" v-for="(item,p_index) in (pay_list)" :key="p_index">
					<view v-if="p_index==0" class="discount-tag">
						<text
							class="discount-text">{{item.first_discount==1 ? item.discount_scale+'折' : '🔥 推荐'}}</text>
					</view>

					<view class="package-name">
						<text>{{item.hono_name}}</text>
					</view>

					<view class="package-price">
						<text class="currency-symbol">￥</text>
						<text class="price-value">{{item.first_discount==1 ? item.avg : item.hono_price}}</text>
					</view>

					<view v-if="item.first_discount==0" class="package-original-price">
						{{item.hono_price}}{{design.currency}}/{{item.hono_name}}
					</view>

					<view v-if="item.first_discount==1" class="package-original-price strikethrough">
						{{item.hono_price}}{{design.currency}}/{{item.hono_name}}
					</view>
				</view>
			</scroll-view>

			<view @tap="pay_check" class="purchase-button">
				<text class="button-icon"></text>
				<text class="button-text">以{{pay_info.first_discount==1 ? pay_info.avg : pay_info.hono_price}}{{design.currency}} 兑换</text>
			</view>
		</view>

		<view v-if="user_honorary.chop_type==1" class="membership-packages">
			<view class="section-title">
				<text class="title-text">🎁 会员套餐</text>
				<text class="subtitle-text">选择适合您的会员套餐</text>
			</view>

			<scroll-view :scroll-x="true" class="packages-scroll">
				<view :class="'package-card ' + (pay_index==p_index ? 'package-selected' : '')" @tap="get_pay_index"
					:data-index="p_index" v-for="(item,p_index) in (pay_list)" :key="p_index">
					<view class="package-duration">
						<text>{{item.time}}天</text>
						<text class="duration-icon">📅</text>
					</view>

					<view class="package-price">
						<text class="currency-symbol">￥</text>
						<text class="price-value">{{item.price}}</text>
					</view>

					<view class="package-original-price">
						{{item.price}}{{design.currency}}/{{item.time}}天
					</view>
				</view>
			</scroll-view>

			<view @tap="pay_check" class="purchase-button">
				<text class="button-icon">💎</text>
				<text class="button-text">以 {{pay_info.price}}{{design.currency}} 兑换</text>
			</view>
		</view>

		<view class="benefits-section">
			<view class="benefits-header">
				<view class="benefits-title">
					<text class="title-main">会员权益</text>
				</view>
				<view class="benefits-subtitle">
					<text>专享特权，尊贵体验</text>
				</view>
			</view>

			<view class="benefits-grid">
				<view class="benefit-card">
					<view class="benefit-icon-wrapper">
						<image :src="(http_root)+'addons/yl_welore/web/static/applet_icon/vip.png'"
							class="benefit-icon"></image>
					</view>
					<view class="benefit-title">专属标注</view>
					<view class="benefit-desc">彰显尊贵权益</view>
				</view>

				<view class="benefit-card">
					<view class="benefit-icon-wrapper">
						<image src="/static/yl_welore/style/icon/zhekou.png" class="benefit-icon"></image>
					</view>
					<view class="benefit-title">折上折</view>
					<view class="benefit-desc">商品折扣更优惠</view>
				</view>

				<view class="benefit-card">
					<view class="benefit-icon-wrapper">
						<image :src="(http_root)+'addons/yl_welore/web/static/applet_icon/jifen.png'"
							class="benefit-icon"></image>
					</view>
					<view class="benefit-title">{{design.confer}}反享</view>
					<view class="benefit-desc">能获得更多{{design.confer}}</view>
				</view>

				<view class="benefit-card">
					<view class="benefit-icon-wrapper">
						<image :src="(http_root)+'addons/yl_welore/web/static/applet_icon/shop.png'"
							class="benefit-icon"></image>
					</view>
					<view class="benefit-title">专属商品</view>
					<view class="benefit-desc">会员专属商品</view>
				</view>

				<view class="benefit-card">
					<view class="benefit-icon-wrapper">
						<image :src="(http_root)+'addons/yl_welore/web/static/applet_icon/ic_category_t5.png'"
							class="benefit-icon"></image>
					</view>
					<view class="benefit-title">颜色标题</view>
					<view class="benefit-desc">会员多标题颜色</view>
				</view>

				<view class="benefit-card">
					<view class="benefit-icon-wrapper">
						<image :src="(http_root)+'addons/yl_welore/web/static/applet_icon/fudai.png'"
							class="benefit-icon"></image>
					</view>
					<view class="benefit-title">专属福利</view>
					<view class="benefit-desc">活动好处惊喜不断</view>
				</view>
			</view>
		</view>

		<view :class="'cu-modal bottom-modal '+(pay?'show':'')" @tap="no_pay">
			<view class="cu-dialog recharge-dialog" catchtap>
				<view class="dialog-header">
					<view class="dialog-title">
						<text class="title-text">充值余额</text>
					</view>
					<view class="dialog-subtitle">选择充值金额</view>
				</view>

				<view class="recharge-options">
					<view @tap="get_pay_money" :data-index="m_index"
						:class="'recharge-option ' + (money_index==m_index ? 'option-selected' : '')"
						v-for="(item,m_index) in (pay_money)" :key="m_index">
						<view v-if="m_index!=0" class="option-content">
							<view class="option-label">{{item.money}}{{design.currency}}</view>
							<view class="option-price">
								<text class="currency-symbol">￥</text>
								<text class="price-value">{{item.money}}</text>
							</view>
						</view>

						<view v-if="m_index==0" class="option-content custom-option">
							<view class="option-label">💰 自定义</view>
							<view class="option-price custom-input">
								<text class="currency-symbol">￥</text>
								<input @input="set_this_money" :value="item.money" placeholder="金额" type="digit"
									class="custom-amount-input" />
							</view>
						</view>
					</view>
				</view>

				<view class="dialog-actions">
					<button class="action-btn cancel-btn" @tap="no_pay"> 取消</button>
					<button class="action-btn confirm-btn" @tap="pay_submit">确认充值</button>
				</view>
			</view>
		</view>

		<view :class="'cu-modal '+(visible_bei?'show':'')">
			<view class="cu-dialog confirm-dialog">
				<view class="confirm-header">
					<view class="confirm-title">
						<text class="title-text">会员兑换确认</text>
					</view>
					<view class="close-btn" @tap="beiClose">
						<text class="cuIcon-close"></text>
					</view>
				</view>

				<view v-if="user_honorary.chop_type==0" class="confirm-content">
					<view class="confirm-message">
						确定以 <text
							class="highlight-text">{{pay_info.first_discount==1?pay_info.avg:pay_info.hono_price}}{{design.currency}}</text>
						兑换 <text class="highlight-text">{{pay_info.hono_name}}</text> 的会员吗？
					</view>
				</view>

				<view v-if="user_honorary.chop_type==1" class="confirm-content">
					<view class="confirm-message">
						确定以 <text class="highlight-text">{{pay_info.price}}{{design.currency}}</text>
						兑换 <text class="highlight-text">{{pay_info.time}}天</text> 的会员吗？
					</view>
				</view>

				<view class="confirm-actions">
					<button class="action-btn cancel-btn" @tap="beiClose">取消</button>
					<button class="action-btn confirm-btn" @tap="beiOk">确认</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import http from "../../../util/http.js";
	import md5 from "../../../util/md5.js";
	const app = getApp();

	export default {
		data() {
			return {
				http_root: app.globalData.http_root,
				user_info: {},
				pay_list: [],
				pay_index: 0,
				pay_info: {},
				visible_bei: false,
				pay: false,
				user_honorary: {},
				pay_money: [{
					money: 1
				}, {
					money: 6
				}, {
					money: 30
				}, {
					money: 68
				}, {
					money: 168
				}, {
					money: 328
				}, {
					money: 648
				}],
				money_index: 0,
				chenck_phone: false,
				copyright: {},
				design: {}
			};
		},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad(options) {
			var e = app.globalData.getCache("userinfo");
			var design = uni.getStorageSync('is_diy');
			this.copyright = getApp().globalData.store.getState().copyright;
			this.design = design;

			if (e.user_phone) {
				this.chenck_phone = true;
			}
		},

		/**
		 * 生命周期函数--监听页面显示
		 */
		onShow() {
			this.get_user_hon();
			this.get_user_info();
		},

		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage() {
			var forward = app.globalData.forward;
			console.log(forward);
			if (forward) {
				return {
					title: forward.title,
					path: '/yl_welore/pages/index/index',
					imageUrl: forward.reis_img
				};
			} else {
				return {
					title: '您的好友给您发了一条信息',
					path: '/yl_welore/pages/index/index'
				};
			}
		},

		methods: {
			/**
			 * 获取手机号
			 */
			getPhoneNumber(c) {
				console.log(c);
				if (c.detail.errMsg == 'getPhoneNumber:ok') {
					var b = app.globalData.api_root + 'User/get_user_phone';
					var that = this;
					var e = app.globalData.getCache("userinfo");
					console.log(e);
					var params = new Object();
					params.token = e.token;
					params.openid = e.openid;
					params.uid = e.uid;
					params.encryptedData = c.detail.encryptedData;
					params.iv = c.detail.iv;
					params.sessionKey = e.session_key;
					http.POST(b, {
						params: params,
						success: function(res) {
							console.log(res);
							if (res.data.status == 'success') {
								var e = app.globalData.getCache("userinfo");
								e.user_phone = res.data.phone;
								console.log(e);
								app.globalData.setCache("userinfo", e);
							}
							uni.showToast({
								title: res.data.msg,
								icon: 'none',
								duration: 2000
							});
							that.chenck_phone = true;
						},
						fail: function() {
							uni.showModal({
								title: '提示',
								content: '网络繁忙，请稍候重试！',
								showCancel: false,
								success: function(res) {}
							});
						}
					});
				} else {
					uni.showModal({
						title: '提示',
						content: c.detail.errMsg,
						showCancel: false,
						success: function(res) {}
					});
				}
			},

			/**
			 * 自定义金额
			 */
			set_this_money(e) {
				var money = e.detail.value;
				var list = this.pay_money;
				list[0]['money'] = money;
				this.pay_money = list;
			},

			/**
			 * 充值贝壳
			 */
			get_pay() {
				this.pay = true;
			},

			/**
			 * 关闭
			 */
			no_pay() {
				this.pay = false;
			},

			/**
			 * 关闭确认框
			 */
			beiClose() {
				this.visible_bei = false;
			},

			/**
			 * 确认
			 */
			beiOk() {
				this.visible_bei = false;
				this.bei_pay();
			},

			/**
			 * 获取会员信息
			 */
			get_user_info() {
				var b = app.globalData.api_root + 'User/get_user_info';
				var that = this;
				var e = app.globalData.getCache("userinfo");
				var params = new Object();
				params.token = e.token;
				params.openid = e.openid;
				http.POST(b, {
					params: params,
					success: function(res) {
						console.log(res);
						if (res.data.status == 'success') {
							that.user_info = res.data.info;
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none',
								duration: 2000
							});
						}
					},
					fail: function() {
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: function(res) {}
						});
					}
				});
			},

			/**
			 * 获取会员价格
			 */
			get_user_hon() {
				var that = this;
				var e = app.globalData.getCache("userinfo");
				var params = new Object();
				params.token = e.token;
				params.openid = e.openid;
				params.uid = e.uid;
				var b = app.globalData.api_root + 'User/get_user_honorary';
				http.POST(b, {
					params: params,
					success: function(res) {
						console.log(res);
						if (res.data.status == "success") {
							that.pay_list = res.data.info;
							that.pay_info = res.data.info[0];
							that.user_honorary = res.data.user_honorary;
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none',
								duration: 2000
							});
						}
					},
					fail: function() {
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: function(res) {}
						});
					}
				});
			},

			/**
			 * 获取选中的
			 */
			get_pay_index(op) {
				var index = op.currentTarget.dataset.index;
				this.pay_index = index;
				this.pay_info = this.pay_list[index];
			},

			/**
			 * 充值金额
			 */
			get_pay_money(op) {
				var index = op.currentTarget.dataset.index;
				this.money_index = index;
			},

			/**
			 * 支付确认
			 */
			pay_check() {
				console.log(this.pay_info);
				this.visible_bei = true;
			},

			/**
			 * 确认开通
			 */
			bei_pay() {
				var that = this;
				var e = app.globalData.getCache("userinfo");
				var params = new Object();
				params.token = e.token;
				params.openid = e.openid;
				params.uid = e.uid;
				params.time = this.pay_info['time'];
				params.pay_index = this.pay_index;
				console.log(params);
				var b = app.globalData.api_root + 'Pay/index';
				http.POST(b, {
					params: params,
					success: function(res) {
						console.log(res);
						if (res.data.status == "success") {
							uni.showToast({
								title: res.data.msg,
								icon: 'none',
								duration: 2000
							});
							that.get_user_info();
							//that.get_user_hon();
						} else {
							if (that.copyright.recharge_arbor == 1) {
								uni.showToast({
									title: res.data.msg,
									icon: 'none',
									duration: 2000
								});
								// if(res.data.code==1&&e.user_phone){
								//   that.get_pay();
								// }
							} else {
								uni.showToast({
									title: res.data.msg,
									icon: 'none',
									duration: 2000
								});
							}
						}
					},
					fail: function() {
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: function(res) {}
						});
					}
				});
			},

			/**
			 * 充值
			 */
			pay_submit() {
				var money = this.pay_money[this.money_index]['money'];
				var that = this;
				var e = app.globalData.getCache("userinfo");
				var params = new Object();
				params.token = e.token;
				params.openid = e.openid;
				params.uid = e.uid;
				params.money = money;
				var b = app.globalData.api_root + 'Pay/do_pay';
				http.POST(b, {
					params: params,
					success: function(res) {
						console.log(res);
						if (res.data.return_msg == "OK") {
							var timeStamp = (Date.parse(new Date()) / 1000).toString();
							var pkg = 'prepay_id=' + res.data.prepay_id;
							var nonceStr = res.data.nonce_str;
							var paySign = md5.hexMD5('appId=' + res.data.appid + '&nonceStr=' + nonceStr +
								'&package=' + pkg + '&signType=MD5&timeStamp=' + timeStamp + "&key=" + res
								.data.app_info['app_key']).toUpperCase(); //此处用到hexMD5插件
							//发起支付
							uni.requestPayment({
								'timeStamp': timeStamp,
								'nonceStr': nonceStr,
								'package': pkg,
								'signType': 'MD5',
								'paySign': paySign,
								success: function(res) {
									uni.showToast({
										title: '充值成功！',
										icon: 'none',
										duration: 2000
									});
									//支付成功之后的操作
									that.get_user_info();
									that.no_pay();
								},
								complete: function() {
									that.get_user_info();
									that.no_pay();
								}
							});
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none',
								duration: 2000
							});
							that.get_pay();
						}
					},
					fail: function() {
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: function(res) {}
						});
					}
				});
			}
		}
	}
</script>
<style>
	/* 页面整体样式 */
	page {
		background-color: #f8f9fc;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
	}

	.page-container {
		padding-bottom: 30rpx;
	}

	/* 会员卡片样式 */
	.member-card {
		margin: 20rpx;
		border-radius: 20rpx;
		background-image: linear-gradient(135deg, #43cbff 0%, #9708cc 100%);
		box-shadow: 0 10rpx 30rpx rgba(67, 203, 255, 0.2);
		overflow: hidden;
		position: relative;
	}

	.member-info-container {
		padding: 30rpx;
		position: relative;
	}

	.user-info-section {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.user-avatar-wrapper {
		position: relative;
		margin-right: 20rpx;
	}

	.member-avatar {
		width: 120rpx !important;
		height: 120rpx !important;
		border: 4rpx solid rgba(255, 255, 255, 0.8);
		box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.vip-crown {
		position: absolute;
		top: -15rpx;
		right: -10rpx;
		font-size: 30rpx;
		z-index: 2;
	}

	.user-details {
		flex: 1;
	}

	.username-row {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.username {
		color: #ffffff;
		font-size: 34rpx;
		font-weight: 600;
		margin-right: 10rpx;
	}

	.vip-badge {
		width: 40rpx;
		height: 40rpx;
		vertical-align: middle;
	}

	.member-status {
		color: rgba(255, 255, 255, 0.9);
		font-size: 26rpx;
	}

	.balance-section {
		margin-top: 20rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.balance-info {
		display: flex;
		align-items: center;
	}

	.currency-icon {
		width: 50rpx;
		height: 50rpx;
		margin-right: 10rpx;
	}

	.balance-amount {
		color: #ffffff;
		font-size: 50rpx;
		font-weight: 600;
	}

	.balance-label {
		color: rgba(255, 255, 255, 0.9);
		font-size: 30rpx;
	}

	.recharge-section {
		position: absolute;
		top: 30rpx;
		right: 30rpx;
	}

	.recharge-btn {
		background: rgba(255, 255, 255, 0.2);
		color: #ffffff;
		border: 2rpx solid rgba(255, 255, 255, 0.5);
		border-radius: 50rpx;
		font-size: 26rpx;
		padding: 10rpx 30rpx;
		box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
		backdrop-filter: blur(5rpx);
	}

	/* 会员套餐区域样式 */
	.membership-packages {
		padding: 30rpx 20rpx;
	}

	.section-title {
		margin-bottom: 20rpx;
		text-align: center;
	}

	.title-text {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-right: 10rpx;
	}

	.subtitle-text {
		font-size: 24rpx;
		color: #999;
		display: block;
		margin-top: 5rpx;
	}

	.packages-scroll {
		white-space: nowrap;
		width: 100%;
		padding: 10rpx 0;
	}

	.package-card {
		display: inline-block;
		width: 220rpx;
		height: 280rpx;
		margin: 15rpx;
		padding: 20rpx;
		border-radius: 20rpx;
		background-color: #ffffff;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		text-align: center;
		position: relative;
		vertical-align: top;
		transition: all 0.3s ease;
	}

	.package-selected {
		transform: translateY(-10rpx);
		box-shadow: 0 10rpx 30rpx rgba(67, 203, 255, 0.2);
		border: 2rpx solid #43cbff;
	}

	.discount-tag {
		position: absolute;
		top: -10rpx;
		right: -10rpx;
		background-image: linear-gradient(135deg, #ff6a88 0%, #ff99ac 100%);
		color: #ffffff;
		font-size: 22rpx;
		padding: 5rpx 15rpx;
		border-radius: 15rpx;
		z-index: 1;
	}

	.package-name,
	.package-duration {
		font-size: 33rpx;
		font-weight: 600;
		color: #333;
		margin-top: 20rpx;
		white-space: normal;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.duration-icon {
		margin-left: 5rpx;
		font-size: 24rpx;
	}

	.package-price {
		margin-top: 20rpx;
	}

	.currency-symbol {
		font-size: 24rpx;
		color: #ff6a88;
		vertical-align: top;
	}

	.price-value {
		font-size: 48rpx;
		font-weight: 700;
		color: #ff6a88;
	}

	.package-original-price {
		font-size: 22rpx;
		color: #999;
		margin-top: 10rpx;
	}

	.strikethrough {
		text-decoration: line-through;
	}

	.package-icon {
		position: absolute;
		bottom: 15rpx;
		right: 15rpx;
		font-size: 24rpx;
		opacity: 0.5;
	}

	.purchase-button {
		height: 90rpx;
		width: 90%;
		margin: 30rpx auto 0;
		border-radius: 50rpx;
		background-image: linear-gradient(135deg, #43cbff 0%, #9708cc 100%);
		color: #ffffff;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 10rpx 20rpx rgba(67, 203, 255, 0.3);
		transition: all 0.3s ease;
	}

	.purchase-button:active {
		transform: scale(0.98);
		box-shadow: 0 5rpx 10rpx rgba(67, 203, 255, 0.2);
	}

	.button-icon {
		margin-right: 10rpx;
	}

	.button-text {
		font-size: 30rpx;
		font-weight: 600;
	}

	/* 会员权益区域样式 */
	.benefits-section {
		padding: 30rpx 20rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		margin: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	}

	.benefits-header {
		text-align: center;
		margin-bottom: 30rpx;
	}

	.benefits-title {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 10rpx;
	}

	.title-emoji {
		font-size: 36rpx;
		margin-right: 10rpx;
	}

	.title-main {
		font-size: 36rpx;
		font-weight: 700;
		color: #333;
	}

	.benefits-subtitle {
		font-size: 24rpx;
		color: #999;
	}

	.benefits-grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}

	.benefit-card {
		width: 30%;
		margin-bottom: 30rpx;
		text-align: center;
	}

	.benefit-icon-wrapper {
		position: relative;
		width: 80rpx;
		height: 80rpx;
		margin: 0 auto 15rpx;
	}

	.benefit-emoji {
		position: absolute;
		top: -10rpx;
		right: -10rpx;
		font-size: 24rpx;
		z-index: 1;
	}

	.benefit-icon {
		width: 80rpx;
		height: 80rpx;
	}

	.benefit-title {
		font-size: 26rpx;
		font-weight: 600;
		color: #ff6a88;
		margin-bottom: 5rpx;
	}

	.benefit-desc {
		font-size: 22rpx;
		color: #999;
	}

	/* 弹窗样式 */
	.recharge-dialog,
	.confirm-dialog {
		border-radius: 20rpx;
		overflow: hidden;
		background-color: #ffffff;
	}

	.dialog-header,
	.confirm-header {
		padding: 30rpx;
		text-align: center;
		position: relative;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.dialog-title,
	.confirm-title {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.title-emoji {
		font-size: 32rpx;
		margin-right: 10rpx;
	}

	.title-text {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
	}

	.dialog-subtitle {
		font-size: 24rpx;
		color: #999;
		margin-top: 5rpx;
	}

	.close-btn {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		font-size: 32rpx;
		color: #999;
	}

	.recharge-options {
		display: flex;
		flex-wrap: wrap;
		padding: 20rpx;
	}

	.recharge-option {
		width: 30%;
		margin: 1.5%;
		height: 150rpx;
		border-radius: 15rpx;
		background-color: #f8f9fc;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
	}

	.option-selected {
		background-image: linear-gradient(135deg, #43cbff 0%, #9708cc 100%);
		box-shadow: 0 5rpx 15rpx rgba(67, 203, 255, 0.3);
	}

	.option-selected .option-label,
	.option-selected .currency-symbol,
	.option-selected .price-value {
		color: #ffffff;
	}

	.option-content {
		text-align: center;
	}

	.option-label {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 10rpx;
	}

	.option-price {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.custom-input {
		display: flex;
		align-items: center;
	}

	.custom-amount-input {
		width: 100rpx;
		font-size: 36rpx;
		color: #ff6a88;
		text-align: center;
	}

	.confirm-content {
		padding: 30rpx;
		text-align: center;
	}

	.confirm-icon {
		font-size: 60rpx;
		margin-bottom: 20rpx;
	}

	.confirm-message {
		font-size: 28rpx;
		color: #666;
		line-height: 1.5;
	}

	.highlight-text {
		color: #ff6a88;
		font-weight: 600;
	}

	.dialog-actions,
	.confirm-actions {
		display: flex;
		border-top: 1rpx solid #f0f0f0;
	}

	.action-btn {
		flex: 1;
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		border: none;
		border-radius: 0;
	}

	.cancel-btn {
		background-color: #f8f9fc;
		color: #999;
	}

	.confirm-btn {
		background-image: linear-gradient(135deg, #43cbff 0%, #9708cc 100%);
		color: #ffffff;
	}
</style>