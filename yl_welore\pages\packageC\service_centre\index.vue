<template>
    <view class="page-container">
        <cu-custom bgColor="none" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">服务中心</view>
        </cu-custom>

        <!-- 功能卡片区域 -->
        <view class="function-section">
            <view class="function-grid">
                <view class="function-item">
                    <navigator url="/yl_welore/pages/packageC/confession/index" hover-class="none">
                        <view class="function-card">
                            <view class="icon-container">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/material/jy.png'" class="function-image"></image>
                            </view>
                            <view class="function-text">申诉</view>
                        </view>
                    </navigator>
                </view>
                <view class="function-item">
                    <navigator url="/yl_welore/pages/packageC/delete_posts/index" hover-class="none">
                        <view class="function-card">
                            <view class="icon-container">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/material/hsz.png'" class="function-image"></image>
                            </view>
                            <view class="function-text">回收站</view>
                        </view>
                    </navigator>
                </view>
                <view class="function-item">
                    <navigator url="/yl_welore/pages/packageC/user_report/index" hover-class="none">
                        <view class="function-card">
                            <view class="icon-container">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/material/jingbao.png'" class="function-image"></image>
                            </view>
                            <view class="function-text">投诉记录</view>
                        </view>
                    </navigator>
                </view>
                <view @tap="open_phone" class="function-item">
                    <view class="function-card">
                        <view class="icon-container">
                            <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/material/shouji.png'" class="function-image"></image>
                        </view>
                        <view v-if="is_phone == false" class="function-text">绑定手机</view>
                        <view v-if="is_phone == true" class="function-text bound">已绑定</view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 分割线 -->
        <!-- <view class="divider"></view> -->

        <!-- 疑问解答标题 -->
        <view class="section-title">
            <text class="title-emoji"></text>
            <text class="title-text">疑问解答</text>
        </view>
        <!-- 疑问解答列表 -->
        <view class="help-list">
            <block v-for="(item, index) in info" :key="index">
                <view class="help-item">
                    <navigator class="help-content" :url="'/yl_welore/pages/packageC/help_info/index?id=' + item.id" hover-class="none">
                        <text class="help-text">{{ item.trouble }}</text>
                        <text class="help-arrow">›</text>
                    </navigator>
                </view>
            </block>
        </view>

        <!-- 服务按钮区域 -->
        <view class="service-section">
            <button
                open-type="contact"
                hover-class="none"
                class="service-button"
            >
                <text class="service-text">在线客服</text>
                <text class="service-arrow">›</text>
            </button>

            <button @tap="caler" hover-class="none" class="service-button">
                <text class="service-text">清理缓存</text>
                <text class="service-arrow">›</text>
            </button>
        </view>

        <!-- 分割线 -->
        <!-- <view class="divider"></view> -->

        <!-- 订阅信息标题 -->
        <view class="section-title">
            <text class="title-text">订阅信息</text>
        </view>
        <!-- 订阅按钮列表 -->
        <view class="subscription-section">
            <button
                @tap="subscription"
                data-key="1"
                hover-class="none"
                class="subscription-button"
            >
                <text class="subscription-text">新的评论提醒</text>
                <text class="subscription-arrow">›</text>
            </button>
            <button
                @tap="subscription"
                data-key="2"
                hover-class="none"
                class="subscription-button"
            >
                <text class="subscription-text">动态点赞通知</text>
                <text class="subscription-arrow">›</text>
            </button>
            <button
                @tap="subscription"
                data-key="3"
                hover-class="none"
                class="subscription-button"
            >
                <text class="subscription-text">帖子被收藏通知</text>
                <text class="subscription-arrow">›</text>
            </button>
            <button
                @tap="subscription"
                data-key="4"
                hover-class="none"
                class="subscription-button"
            >
                <text class="subscription-text">新的回复提醒</text>
                <text class="subscription-arrow">›</text>
            </button>
            <button
                @tap="subscription"
                data-key="5"
                hover-class="none"
                class="subscription-button"
            >
                <text class="subscription-text">申请加入通知</text>
                <text class="subscription-arrow">›</text>
            </button>
            <button
                @tap="subscription"
                data-key="6"
                hover-class="none"
                class="subscription-button"
            >
                <text class="subscription-text">留言提醒</text>
                <text class="subscription-arrow">›</text>
            </button>
            <button
                @tap="subscription"
                data-key="7"
                hover-class="none"
                class="subscription-button"
            >
                <text class="subscription-text">收到赞赏通知</text>
                <text class="subscription-arrow">›</text>
            </button>
            <button
                @tap="subscription"
                data-key="8"
                hover-class="none"
                class="subscription-button"
            >
                <text class="subscription-text">审核结果通知</text>
                <text class="subscription-arrow">›</text>
            </button>
            <button
                @tap="subscription"
                data-key="9"
                hover-class="none"
                class="subscription-button"
            >
                <text class="subscription-text">站内信提醒</text>
                <text class="subscription-arrow">›</text>
            </button>
            <button
                @tap="subscription"
                data-key="10"
                hover-class="none"
                class="subscription-button"
            >
                <text class="subscription-text">事件处理进度通知</text>
                <text class="subscription-arrow">›</text>
            </button>
            <button
                @tap="subscription"
                data-key="11"
                hover-class="none"
                class="subscription-button"
            >
                <text class="subscription-text">认证审核通知</text>
                <text class="subscription-arrow">›</text>
            </button>
        </view>

        <!-- 底部间距 -->
        <view class="bottom-spacing"></view>
        <phone id="phone" :check_phone="check_phone"></phone>
    </view>
</template>

<script>
import phone from '@/yl_welore/util/user_phone/phone';
var app = getApp();
var http = require('../../../util/http.js');
export default {
    components: {
        phone
    },
    data() {
        return {
            http_root: app.globalData.http_root,
            info: [],
            di_msg: false,
            check_phone: false,
            is_phone: false
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        var e = app.globalData.getCache('userinfo');
        console.log(e);
        if (e.user_phone) {
            this.is_phone = true;
        }
        var subscribe = app.globalData.getCache('subscribe');
        if (!subscribe) {
            app.globalData.subscribe_message(
                (res) => {
                    //请求成功的回调函数
                    console.log(res);
                    if (res == '') {
                        return;
                    }
                    app.globalData.setCache('subscribe', res.parallelism_data);
                },
                () => {
                    //请求失败的回调函数，不需要时可省略
                }
            );
        }
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        this.get_help_info();
        //this.get_user_amount();
    },
    /**
     * 加载下一页
     */
    onReachBottom() {},
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        var forward = app.globalData.forward;
        console.log(forward);
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/index/index',
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/index/index'
            };
        }
    },
    methods: {
        open_phone() {
            var e = app.globalData.getCache('userinfo');
            if (e.user_phone) {
                this.is_phone = true;
                return;
            }
            this.check_phone = true;
        },

        subscription(key) {
            var subscribe = app.globalData.getCache('subscribe');
            if (key.currentTarget.dataset.key == 1 || key.currentTarget.dataset.key == 2 || key.currentTarget.dataset.key == 3) {
                if (subscribe && subscribe['YL0001'] && subscribe['YL0002'] && subscribe['YL0003']) {
                    app.globalData.authorization(subscribe['YL0001'], subscribe['YL0002'], subscribe['YL0003'], (res) => {});
                }
            } else if (key.currentTarget.dataset.key == 4 || key.currentTarget.dataset.key == 5 || key.currentTarget.dataset.key == 6) {
                if (subscribe && subscribe['YL0004'] && subscribe['YL0005'] && subscribe['YL0006']) {
                    app.globalData.authorization(subscribe['YL0001'], subscribe['YL0005'], subscribe['YL0006'], (res) => {});
                }
            } else if (key.currentTarget.dataset.key == 7 || key.currentTarget.dataset.key == 8 || key.currentTarget.dataset.key == 9) {
                if (subscribe && subscribe['YL0007'] && subscribe['YL0008'] && subscribe['YL0009']) {
                    app.globalData.authorization(subscribe['YL0007'], subscribe['YL0008'], subscribe['YL0009'], (res) => {});
                }
            } else {
                if (subscribe && subscribe['YL0010']) {
                    app.globalData.authorization(subscribe['YL0010'], subscribe['YL0011'], subscribe['YL0009'], (res) => {});
                }
            }
        },

        caler() {
            uni.removeStorage({
                key: 'is_diy',
                success(res) {
                    uni.showToast({
                        title: '清理成功！',
                        icon: 'none',
                        duration: 2000
                    });
                }
            });
            uni.removeStorage({
                key: 'subscribeqtz',
                success(res) {}
            });
            uni.removeStorage({
                key: 'yl_versionqtz',
                success(res) {}
            });
        },

        /**
         * 获取会员信息
         */
        get_help_info() {
            var b = app.globalData.api_root + 'User/get_help_info';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    if (res.data.status == 'success') {
                        that.info = res.data.info;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
/* 页面容器 */
.page-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* 功能区域样式 */
.function-section {
    padding: 20rpx 30rpx;
    background: transparent;
    margin-bottom: 20rpx;
}

.function-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15rpx;
}

.function-item {
    position: relative;
}

.function-card {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 25rpx 15rpx 20rpx;
    text-align: center;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.function-card:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.12);
}

.function-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.function-card:active::before {
    opacity: 1;
}

.icon-container {
    position: relative;
    margin-bottom: 12rpx;
    display: flex;
    justify-content: center;
    align-items: center;
}

.emoji-icon {
    font-size: 24rpx;
    position: absolute;
    top: -8rpx;
    right: 10rpx;
    z-index: 2;
}

.function-image {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 8rpx;
}

.function-text {
    color: #374151;
    font-size: 22rpx;
    font-weight: 500;
    line-height: 1.2;
}

.function-text.bound {
    color: #10b981;
    font-weight: 600;
}

/* 分割线样式 */
.divider {
    height: 20rpx;
    background: linear-gradient(90deg, #f1f3f4 0%, #e8eaed 50%, #f1f3f4 100%);
    margin: 20rpx 0;
}

/* 标题样式 */
.section-title {
    display: flex;
    align-items: center;
    padding: 30rpx 40rpx 20rpx;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10rpx);
    margin-bottom: 20rpx;
}

.title-emoji {
    font-size: 36rpx;
    margin-right: 15rpx;
}

.title-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #374151;
}

/* 帮助列表样式 */
.help-list {
    margin: 0 30rpx 20rpx;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.help-item {
    border-bottom: 1rpx solid #f3f4f6;
}

.help-item:last-child {
    border-bottom: none;
}

.help-content {
    display: flex;
    align-items: center;
    padding: 30rpx;
    transition: background-color 0.2s ease;
}

.help-content:active {
    background-color: #f9fafb;
}

.help-emoji {
    font-size: 32rpx;
    margin-right: 20rpx;
}

.help-text {
    flex: 1;
    font-size: 28rpx;
    color: #374151;
    line-height: 1.5;
}

.help-arrow {
    font-size: 32rpx;
    color: #9ca3af;
    font-weight: 300;
}

/* 服务按钮样式 */
.service-section {
    margin: 0 30rpx 20rpx;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.service-button {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 30rpx;
    background: transparent;
    border: none;
    border-bottom: 1rpx solid #f3f4f6;
    font-size: 28rpx;
    text-align: left;
    transition: background-color 0.2s ease;
}

.service-button:last-child {
    border-bottom: none;
}

.service-button:active {
    background-color: #f9fafb;
}

.service-emoji {
    font-size: 32rpx;
    margin-right: 20rpx;
}

.service-text {
    flex: 1;
    color: #374151;
    font-weight: 500;
}

.service-arrow {
    font-size: 32rpx;
    color: #9ca3af;
    font-weight: 300;
}

/* 订阅按钮样式 */
.subscription-section {
    margin: 0 30rpx 30rpx;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.subscription-button {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 30rpx;
    background: transparent;
    border: none;
    border-bottom: 1rpx solid #f3f4f6;
    font-size: 26rpx;
    text-align: left;
    transition: all 0.2s ease;
}

.subscription-button:last-child {
    border-bottom: none;
}

.subscription-button:active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scale(0.98);
}

.subscription-button:active .subscription-text,
.subscription-button:active .subscription-arrow {
    color: #ffffff;
}

.subscription-emoji {
    font-size: 28rpx;
    margin-right: 20rpx;
}

.subscription-text {
    flex: 1;
    color: #374151;
    font-weight: 400;
    transition: color 0.2s ease;
}

.subscription-arrow {
    font-size: 28rpx;
    color: #9ca3af;
    font-weight: 300;
    transition: color 0.2s ease;
}

/* 底部间距 */
.bottom-spacing {
    height: 60rpx;
}

/* 重置按钮样式 */
button::after {
    display: none;
}

button {
    border-radius: 0;
    line-height: normal;
    padding: 0;
    margin: 0;
    background-color: transparent;
    font-size: inherit;
    overflow: visible;
}

/* 页面背景 */
page {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}
</style>
