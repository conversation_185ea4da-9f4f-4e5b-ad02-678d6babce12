<template>
  <view>
    <cu-custom bgColor="bg-white" :isBack="true" :isSearch="false">
      <view slot="backText">返回</view>
      <view slot="content" style="color: #2c2b2b;font-weight: 600; font-size: 36rpx;">{{ gambit_name }}</view>
    </cu-custom>

    <view :class="'cu-modal ' + (modalName == 'RadioModal' ? 'show' : '')" @tap="hideModal">
      <view class="cu-dialog" catchtap>
        <view class="cu-list menu text-left">
          <view class="cu-item" @tap="handleClickItem1" :data-index="a_index" v-for="(item, a_index) in (actions)"
            :key="a_index">
            <view class="flex-sub">{{ item.name }}</view>
            <text v-if="item.type == this_pattern" class="cuIcon-roundcheckfill text-green"></text>
          </view>
        </view>
      </view>
    </view>

    <Index1 ref="indexComp" v-if="$state.diy.mod.home == '0'" :parentData="currentInstance" @home-url="home_url"
      @gambit-list="gambit_list" @dian-option="dian_option" @vote-do="vote_do" @play="play" @stop="stop"
      @slider-change="sliderChange" @home-pl="home_pl" @dynamic-code="handleDynamicCode"></Index1>
    <Index2 ref="indexComp" v-if="$state.diy.mod.home == 'aa3b1c88-2d41-9cde-cff7-55372169e4eb'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index2>
    <Index3 ref="indexComp" v-if="$state.diy.mod.home == 'be454a15-e373-f773-376b-127f3a35d3c6'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index3>
    <Index4 ref="indexComp" v-if="$state.diy.mod.home == '453776a4-6724-fd4f-4ff1-48363b245915'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index4>
    <Index5 ref="indexComp" v-if="$state.diy.mod.home == 'd5b2d78e-3152-ee54-aca8-a4402adc601b'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index5>
    <Index6 ref="indexComp" v-if="$state.diy.mod.home == '58567383-612e-ca8f-116d-89e1057eb02a'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index6>
    <Index7 ref="indexComp" v-if="$state.diy.mod.home == '9701db92-a7e1-bdd7-842e-9e9bea168127'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index7>
    <Index8 ref="indexComp" v-if="$state.diy.mod.home == '47a436a1-6541-a7a0-5723-61d7fe40b7c3'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index8>
    <Index9 ref="indexComp" v-if="$state.diy.mod.home == '57228047-ad66-b5c0-2970-0be62de79377'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index9>
  </view>
</template>

<script>
import Index1 from "@/yl_welore/pages/index/index1.vue";
import Index2 from "@/yl_welore/pages/index/index2.vue";
import Index3 from "@/yl_welore/pages/index/index3.vue";
import Index4 from "@/yl_welore/pages/index/index4.vue";
import Index5 from "@/yl_welore/pages/index/index5.vue";
import Index6 from "@/yl_welore/pages/index/index6.vue";
import Index7 from "@/yl_welore/pages/index/index7.vue";
import Index8 from "@/yl_welore/pages/index/index8.vue";
import Index9 from "@/yl_welore/pages/index/index9.vue";
var app = getApp();
var http = require("../../util/http.js");
const innerAudioContext = uni.getBackgroundAudioManager();
export default {
  components: {
    Index1,
    Index2,
    Index3,
    Index4,
    Index5,
    Index6,
    Index7,
    Index8,
    Index9
  },
  /**
   * 页面的初始数据
   */
  data() {
    return {
      modalName:null,
      http_root: app.globalData.http_root,
      this_pattern: 'img',
      actions_name: '图文模式',
      actions: [{
        name: '图文模式',
        type: 'img'
      }, {
        name: '简洁模式',
        type: 'concise'
      }],
      page: 1,
      new_list: [],
      di_msg: false,
      gambit_name: '',
      money: 0,
      money_id: 0,
      money_index: 0,
      home_list: [],
      admin: 0,
      mod: '',
      design: {},
      isPopping: false,
      //是否已经弹出
      animPlus: {},
      //旋转动画
      animCollect: {},
      //item位移,透明度
      animTranspond: {},
      //item位移,透明度
      animInput: {},
      //item位移,透明
      animBack: {},
      animationHD: {},
      version: 0,
      check_phone: false,
      home_pl_check: false,
      images: []
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    var e = app.globalData.getCache("userinfo");
    var design = uni.getStorageSync('is_diy');
    var pattern = app.globalData.getCache("pattern");
    if (pattern) {
      this.this_pattern = pattern.type;
      this.actions_name = pattern.name;
    }
    this.design = design;
    this.mod = design.mod;
    this.id = options.id;
    this.copyright = getApp().globalData.store.getState().copyright;
    this.add = design.pattern_data.release.list;
    this.uid = e.uid;
    //console.log(design);
    this.get_gambit_list();
    this.get_user_info();
  },
  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    uni.showNavigationBarLoading(); //在标题栏中显示加载
    //模拟加载
    setTimeout(function () {
      uni.hideNavigationBarLoading(); //完成停止加载
      uni.stopPullDownRefresh(); //停止下拉刷新
    }, 1500);
    this.new_list = [];
    this.page = 1;
    this.get_gambit_list();

    //this.get_ad();
  },
  /**
   * 加载下一页
   */
  onReachBottom() {
    this.page = this.page + 1;
    this.get_gambit_list();
  },
  onShow() {
    this.takeback();
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage(d) {
    var forward = app.globalData.forward;
    if (d.from == 'menu') {
      if (forward) {
        return {
          title: forward.title,
          path: '/yl_welore/pages/index/index',
          imageUrl: forward.reis_img
        };
      } else {
        return {
          title: this.copyright['title'],
          path: '/yl_welore/pages/index/index'
        };
      }
    }
    var key = d.target.dataset.key;
    var info = this.new_list[key];
    if (forward) {
      return {
        title: forward.title,
        path: '/yl_welore/pages/packageA/article/index?id=' + info.id + '&type=' + info.info_type,
        imageUrl: forward.reis_img
      };
    } else {
      let img;
      if (info.image_part) {
        if (info.image_part.length > 0) {
          img = info.image_part[0];
        }
      }
      return {
        title: info.study_title == '' ? info.study_content : info.study_title,
        path: '/yl_welore/pages/packageA/article/index?id=' + info.id + '&type=' + info.info_type,
        imageUrl: img
      };
    }
  },
  methods: {
    by_url(item) {
      var info = this.new_list[item.target.dataset.k];
      var videoContext = uni.createVideoContext('myVideo' + info['id']);
      if (info['check_look'] == 0) {
        videoContext.stop();
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/article/index?id=' + info['id'] + '&type=' + info['study_type']
        });
      }
    },
    /**
     * 首页跳转链接
     */
    home_url(dd) {
      var warrant_arbor = getApp().globalData.store.getState().copyright['warrant_arbor'];
      var that = this;
      var key = dd.currentTarget.dataset.k; //跳转类型
      if (key == 1) {
        //头像跳转
        uni.navigateTo({
          url: '/yl_welore/pages/packageB/my_home/index?id=' + dd.currentTarget.dataset.user_id
        });
        return;
      }
      if (key == 2) {
        //圈子跳转
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/circle_info/index?id=' + dd.currentTarget.dataset.id
        });
        return;
      }
      var e = app.globalData.getCache("userinfo");
      if (e.tourist == 1 && warrant_arbor == 1) {
        this.check_user_login = true;
        return;
      }
      if (key == 3) {
        //内容跳转
        var douyin = app.globalData.__PlugUnitScreen('5fb4baf1f25fe251685b526dc8c30b8f');
        var info = this.new_list[dd.currentTarget.dataset.index];
        if (dd.currentTarget.dataset.type == 2 && info.is_buy == 0 && e.user_phone && douyin) {
          uni.navigateTo({
            url: '/yl_welore/pages/packageF/full_video/index?id=' + dd.currentTarget.dataset.id
          });
          return;
        }
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/article/index?id=' + dd.currentTarget.dataset.id + '&type=' + dd.currentTarget.dataset.type
        });
        return;
      }
    },
    //点击弹出
    plus() {
      var that = this;
      console.log(that.isPopping);
      if (that.isPopping == false) {
        //弹出动画
        that.popp();
        that.isPopping = true;
      } else if (that.isPopping == true) {
        //缩回动画 
        that.takeback();
        that.isPopping = false;
      }
    },
    //弹出动画
    popp() {
      var that = this;
      //plus顺时针旋转
      var animationPlus = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationcollect = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationTranspond = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationInput = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animBackCollect = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationM = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationHD = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      animationPlus.rotateZ(225).step();
      var copyright = getApp().globalData.store.getState().copyright;
      var myCars = new Array();
      myCars[0] = [[25, -105], [25, -105]];
      myCars[1] = [[-50, -105], [100, -105]];
      myCars[2] = [[-85, -105], [30, -105], [140, -105]];
      myCars[3] = [[-100, -105], [-15, -105], [75, -105], [155, -105]];
      myCars[4] = [[-125, -105], [25, -105], [100, -105], [-50, -105], [170, -105]];
      var tab = [animationInput];
      if (copyright['version'] == 1) {
        //图文
      } else {
        //语音
        if (copyright['hair_audio_arbor'] == 1) {
          tab.push(animationTranspond);
        }
        //投票
        if (copyright['hair_vote_arbor'] == 1) {
          tab.push(animationcollect);
        }
        //视频
        if (copyright['hair_video_arbor'] == 1) {
          tab.push(animationM);
        }
        //活动
        if (copyright['hair_brisk_arbor'] == 1) {
          tab.push(animationHD);
        }
      }
      var key = tab.length - 1;
      for (var i = 0; i < tab.length; i++) {
        tab[i].translate(myCars[key][i][0], myCars[key][i][1]).rotateZ(360).opacity(1).height('50px').width('50px').step();
      }
      animBackCollect.backgroundColor('#F7F9FA').height(190).step();
      that.animPlus = animationPlus.export();
      that.animCollect = animationcollect.export();
      that.animTranspond = animationTranspond.export();
      that.animInput = animationInput.export();
      that.animationM = animationM.export();
      that.animBack = animBackCollect.export();
      that.animationHD = animationHD.export();
    },
    //收回动画
    takeback() {
      var that = this;
      //plus逆时针旋转
      var animationPlus = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationcollect = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationTranspond = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationInput = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animBackCollect = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationM = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationHD = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      animationPlus.rotateZ(0).step();
      animationcollect.translate(0, 0).rotateZ(0).opacity(0).height('0rpx').width('0rpx').step();
      animationTranspond.translate(0, 0).rotateZ(0).opacity(0).height('0rpx').width('0rpx').step();
      animationInput.translate(0, 0).rotateZ(0).opacity(0).height('0rpx').width('0rpx').step();
      animationM.translate(0, 0).rotateZ(0).opacity(0).height('0rpx').width('0rpx').step();
      animationHD.translate(0, 0).rotateZ(0).opacity(0).height('0rpx').width('0rpx').step();
      animBackCollect.backgroundColor('transparent').height(45).step();
      that.animPlus = animationPlus.export();
      that.animCollect = animationcollect.export();
      that.animTranspond = animationTranspond.export();
      that.animInput = animationInput.export();
      that.animationM = animationM.export();
      that.animBack = animBackCollect.export();
      that.animationHD = animationHD.export();
    },
    //播放声音
    play(e) {
      var that = this;
      var warrant_arbor = getApp().globalData.store.getState().copyright['warrant_arbor'];
      var user = app.globalData.getCache("userinfo");
      if (user.tourist == 1 && warrant_arbor == 1) {
        this.check_user_login = true;
        return;
      }
      var index = e.currentTarget.dataset.key;
      var nuw = this.new_list;
      var key = 1;
      var info = this.new_list[index];
      if (info['check_look'] == 0) {
        innerAudioContext.stop();
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/article/index?id=' + info['id'] + '&type=' + info['study_type']
        });
        return;
      }
      uni.getBackgroundAudioPlayerState({
        success(res) {
          console.log(res);
          const status = res.status;
          key = res.status;
        }
      });
      for (var i = 0; i < nuw.length; i++) {
        nuw[i]['is_voice'] = false;
      }
      this.new_list = nuw;
      console.log('播放');
      innerAudioContext.src = e.currentTarget.dataset.vo;
      innerAudioContext.title = nuw[index]['study_title'] ? nuw[index]['study_title'] : '暂无标题';
      innerAudioContext.onTimeUpdate(() => {
        //console.log(innerAudioContext.currentTime)
        var duration = innerAudioContext.duration;
        var offset = innerAudioContext.currentTime;
        var currentTime = parseInt(innerAudioContext.currentTime);
        var min = "0" + parseInt(currentTime / 60);
        var sec = currentTime % 60;
        if (sec < 10) {
          sec = "0" + sec;
        }
        ;
        var starttime = min + ':' + sec; /*  00:00  */

        nuw[index]['starttime'] = starttime;
        nuw[index]['offset'] = offset;
        this.new_list = nuw;
      });
      // innerAudioContext.play();

      nuw[index]['is_voice'] = true;
      this.new_list = nuw;
      this.new_list_index = index;
      //播放结束
      innerAudioContext.onEnded(() => {
        var nuw = this.new_list;
        nuw[index]['is_voice'] = false;
        that.starttime = '00:00';
        that.offset = 0;
        that.new_list = nuw;
        console.log("音乐播放结束");
      });
      innerAudioContext.play();
    },
    /**
     * 停止
     */
    stop(e) {
      innerAudioContext.pause();
      console.log('暂停');
      var index = e.currentTarget.dataset.key;
      var nuw = this.new_list;
      nuw[index]['is_voice'] = false;
      this.new_list = nuw;
    },
    // 进度条拖拽
    sliderChange(e) {
      var that = this;
      var index = e.currentTarget.dataset.key;
      var nuw = this.new_list;
      var info = this.new_list[index];
      if (info['check_look'] == 0) {
        innerAudioContext.stop();
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/article/index?id=' + info['id'] + '&type=' + info['study_type']
        });
        return;
      }
      var offset = parseInt(e.detail.value);
      innerAudioContext.play();
      innerAudioContext.seek(offset);
      var nuw = this.new_list;
      nuw[index]['is_voice'] = true;
      this.new_list = nuw;
    },
    /**
     * 打开地图
     */
    get_position(d) {
      var a = Number(d.currentTarget.dataset.latitude);
      var o = Number(d.currentTarget.dataset.longitude);
      var name = d.currentTarget.dataset.pos_name;
      if (a && o) {
        uni.openLocation({
          latitude: a,
          longitude: o,
          name: name
        });
      }
    },
    /**
     * 获取话题列表
     */
    get_gambit_list() {
      var b = app.globalData.api_root + 'User/get_gambit_list';
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.uid = e.uid;
      params.token = e.token;
      params.openid = e.openid;
      params.id = this.id;
      params.index_page = this.page;
      params.pattern = this.this_pattern;
      var allMsg = this.new_list;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == 'success') {
            for (var i = 0; i < res.data.info.length; i++) {
              allMsg.push(res.data.info[i]);
            }
            this.new_list = allMsg;
            this.gambit_name = res.data.gambit_name;
            this.version = res.data.version;
            if (res.data.info.length == 0 || allMsg.length < 3) {
              this.di_msg = true;
            }
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    hideModal() {
      this.check_phone = false;
      this.home_pl_check = false;
      this.purchase_paper_mod = false;
      this.modalName = null;
    },
    //首页评论
    home_pl(e) {
      //console.log(e);
      this.home_pl_check = true;
      this.pl_id = e.currentTarget.dataset.id;
      this.pl_key = e.currentTarget.dataset.key;
    },
    //评论采集
    home_pl_cai(e) {
      this.home_pl_text = e.detail.value;
    },
    preventTouchMove() { },
    /**
     * 获取手机号
     */
    getPhoneNumber(c) {
      if (c.detail.errMsg == 'getPhoneNumber:ok') {
        var b = app.globalData.api_root + 'User/get_user_phone';
        var e = app.globalData.getCache("userinfo");
        var params = new Object();
        params.token = e.token;
        params.openid = e.openid;
        params.uid = e.uid;
        params.encryptedData = c.detail.encryptedData;
        params.iv = c.detail.iv;
        params.sessionKey = e.sessionKey;
        console.log(params);
        http.POST(b, {
          params: params,
          success: (res) => {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            this.check_phone = false;
            this.get_user_info();
          },
          fail: () => {
            uni.showModal({
              title: '提示',
              content: '网络繁忙，请稍候重试！',
              showCancel: false,
              success: function (res) { }
            });
          }
        });
      }
    },
    //获取用户信息
    get_user_info() {
      var b = app.globalData.api_root + 'User/get_user_info';
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == 'success') {
            this.user_info = res.data.info;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    /**
     * 跳转
     */
    nav_add(d) {
      this.copyright = getApp().globalData.store.getState().copyright;
      var k = d.currentTarget.dataset.k;
      var diy = this.design;
      if (this.copyright['force_phone_arbor'] == 1 && !this.user_info['user_phone'] && this.version == 0) {
        this.check_phone = true;
        return;
      }
      if (k == 'tuwen') {
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/add/index?type=0&fa_class=&name=&gambit_name=' + this.gambit_name + '&gambit_id=' + this.id
        });
      }
      if (k == 'toupiao') {
        //判断是否开启限制
        if (diy.user_vip['vote_member'] == 1) {
          if (diy['vip'] == 1) {
            uni.navigateTo({
              url: '/yl_welore/pages/packageA/add/index?type=6&fa_class=&name=&gambit_name=' + this.gambit_name + '&gambit_id=' + this.id
            });
          } else {
            uni.showToast({
              title: 'VIP专属',
              icon: 'none',
              duration: 2000
            });
            return;
          }
        } else {
          uni.navigateTo({
            url: '/yl_welore/pages/packageA/add/index?type=6&fa_class=&name=&gambit_name=' + this.gambit_name + '&gambit_id=' + this.id
          });
        }
      }
      if (k == 'yuyin') {
        //判断是否开启限制
        if (diy.user_vip['voice_member'] == 1) {
          if (diy['vip'] == 1) {
            uni.navigateTo({
              url: '/yl_welore/pages/packageA/add/index?type=1&fa_class=&name=&gambit_name=' + this.gambit_name + '&gambit_id=' + this.id
            });
          } else {
            uni.showToast({
              title: 'VIP专属',
              icon: 'none',
              duration: 2000
            });
            return;
          }
        } else {
          uni.navigateTo({
            url: '/yl_welore/pages/packageA/add/index?type=1&fa_class=&name=&gambit_name=' + this.gambit_name + '&gambit_id=' + this.id
          });
        }
      }
      if (k == 'shipin') {
        //判断是否开启限制
        if (diy.user_vip['video_member'] == 1) {
          if (diy['vip'] == 1) {
            uni.navigateTo({
              url: '/yl_welore/pages/packageA/add/index?type=2&fa_class=&name=&gambit_name=' + this.gambit_name + '&gambit_id=' + this.id
            });
          } else {
            uni.showToast({
              title: 'VIP专属',
              icon: 'none',
              duration: 2000
            });
            return;
          }
        } else {
          uni.navigateTo({
            url: '/yl_welore/pages/packageA/add/index?type=2&fa_class=&name=&gambit_name=' + this.gambit_name + '&gambit_id=' + this.id
          });
        }
      }
      if (k == 'huodong') {
        //判断是否开启限制
        if (diy.user_vip['brisk_member'] == 1) {
          if (diy['vip'] == 1) {
            uni.navigateTo({
              url: '/yl_welore/pages/packageA/add/index?type=4&fa_class=' + this.getInfo.id + '&name=' + this.getInfo.realm_name + '&gambit_name=&gambit_id=0'
            });
          } else {
            uni.showToast({
              title: 'VIP专属',
              icon: 'none',
              duration: 2000
            });
            return;
          }
        } else {
          uni.navigateTo({
            url: '/yl_welore/pages/packageA/add/index?type=4&fa_class=' + this.getInfo.id + '&name=' + this.getInfo.realm_name + '&gambit_name=&gambit_id=0'
          });
        }
      }
    },
    /**
     * 点赞
     */
    add_zan(data) {
      var id = data.currentTarget.dataset.id;
      var key = data.currentTarget.dataset.key;
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.id = id;
      params.uid = e.uid;
      params.applaud_type = 0;
      params.zan_type = this.new_list[key]['is_info_zan'] == true ? 1 : 0;
      var list = that.new_list;
      uni.vibrateShort();
      var list_a = 'new_list[' + key + '].is_info_zan';
      var list_c = 'new_list[' + key + '].info_zan_count_this';
      if (list[key]['is_info_zan'] == false) {
        this.$set(that.new_list[key], 'is_info_zan', true);
        this.$set(that.new_list[key], 'info_zan_count_this', list[key]['info_zan_count_this'] + 1);
      } else {
        this.$set(that.new_list[key], 'is_info_zan', false);
        this.$set(that.new_list[key], 'info_zan_count_this', list[key]['info_zan_count_this'] - 1);
      }
      that.rotate3d(key);
      var b = app.globalData.api_root + 'User/add_user_zan';
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") { } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    /**
     * 动画
     */
    rotate3d(key) {
      var that = this;
      var list = that.new_list;
      // 创建一个动画实例
      var animation_zan = uni.createAnimation({
        // 动画持续时间
        duration: 300,
        // 定义动画效果，当前是匀速
        timingFunction: 'ease'
      });
      // 将该变量赋值给当前动画
      that.animation_zan = animation_zan;
      // 先在y轴偏移，然后用step()完成一个动画

      animation_zan.rotate3d(0, 1, 0, 180).step();
      this.$set(that.new_list[key], 'animationData_zan', animation_zan.export());
      setTimeout(function () {
        var list_g = that.new_list;
        animation_zan.rotate3d(0, 1, 0, 0).step();
        that.$set(list_g[key], 'animationData_zan', animation_zan.export());
      }, 100);
    },
    //购买付费帖子
    purchase_paper(data) {
      var id = data.currentTarget.dataset.id;
      var money = data.currentTarget.dataset.money;
      var index = data.currentTarget.dataset.index;
      this.money = money;
      this.money_id = id;
      this.purchase_paper_mod = true;
      this.money_index = index;
    },
    //do
    do_paper_money() {
      var b = app.globalData.api_root + 'User/do_paper_money_new';
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.money_id = this.money_id;
      params.money = this.money;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == 'success') {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            var list = 'new_list[' + this.money_index + '].purchase';
            this.$set(this.new_list[this.money_index], 'purchase', '1');
            this.purchase_paper_mod = false;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    /**
     * 是否禁止转发
     */
    check_share() {
      uni.showToast({
        title: '该帖禁止转发',
        icon: 'none',
        duration: 2000
      });
      return;
    },
    showModal(e) {
      console.log(e);
      this.modalName = 'RadioModal';
    },
    handleClickItem1(detail) {
      var index = detail.currentTarget.dataset.index;
      var actions = this.actions;
      var select = actions[index];
      console.log(select);
      this.modalName = null;
      this.actions_name = select['name'];
      this.this_pattern = select['type'];
      this.page = 1;
      this.new_list = [];
      // var order = new Object();
      // order.type = select['type'];
      // order.name = select['name'];
      app.globalData.setCache("pattern", select);
      // var order_i = app.getCache("order_actions");
      this.get_gambit_list();
    }
  }
};
</script>
<style>
._this {
  font-weight: 700;
  font-size: 25px;
}

.banner-swiper {
  width: 100%;
  height: 500rpx;
  overflow: hidden;
}


.slide-image {
  width: 96%;
  display: block;
  margin: 0 auto;
  height: 450rpx;
}

.active {
  margin-top: 0rpx;
  height: 500rpx;
}

page {
  background-color: #ffffff;
}

.nav-wrap {
  position: fixed;
  width: 100%;
  top: 0;
  background: #fff;
  color: #000;
  z-index: 9999999;
}

/* 标题要居中 */
.nav-title {
  position: absolute;
  text-align: center;
  max-width: 377rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  font-size: 30rpx;
  color: #2c2b2b;
  font-weight: 600;
}

.nav-capsule {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  width: 50rpx;
  justify-content: space-around;
  border-radius: 50%;
  margin-top: 54rpx;
  z-index: 999999999;
}

.navbar-v-line {
  width: 1px;
  height: 32rpx;
  background-color: #F3F3F3;
}

.back-pre {
  width: 35px;
  margin-top: 11rpx;
  margin-left: 10px;
}

button::after {
  line-height: normal;
  font-size: 30rpx;
  width: 0;
  height: 0;
  top: 0;
  left: 0;
}

button {
  line-height: normal;
  display: block;
  padding-left: 0px;
  padding-right: 0px;
  background-color: rgba(255, 255, 255, 0);
  font-size: 30rpx;
  overflow: inherit;
}

.pinglunzan {
  margin-top: 20rpx;
}

.demo-row {
  padding: 10px 0px;
}

.yes_pos {
  position: relative;
}

.text_num {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  white-space: normal !important;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  font-size: 16px;
  letter-spacing: 1px;
}

.placeholder {
  padding: 0rpx 5px 5rpx 5rpx;
}

.img-style {
  height: 0rpx;
  width: 0rpx;
  position: absolute;
  right: 50%;
  opacity: 0;
  bottom: 0px;
}

.weui-tabbar_boo {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  position: fixed;
  bottom: 0;
  width: 100%;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
}

.weui-tabbar_boo_no {
  /* display: -webkit-box;
  display: -webkit-flex; */
  position: fixed;
  bottom: 23%;
  right: 0;
  /* width: 100%; */
}


/**
     * 弹窗
     */
.show-btn {
  margin-top: 100rpx;
  color: #22cc22;
}

.modal-mask {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  opacity: 0.5;
  overflow: hidden;
  z-index: 888;
  color: #fff;
}

.modal-dialog {
  width: 540rpx;
  position: fixed;
  top: 45%;
  left: 0;
  z-index: 999;
  background: #f9f9f9;
  margin: -180rpx 105rpx;
  border-radius: 10px;
}

.modal-title {
  padding-top: 50rpx;
  font-size: 36rpx;
  color: #030303;
  text-align: center;
}

.modal-content {
  padding: 20rpx 32rpx;
}

.modal-input {
  display: flex;
  background: #fff;
  border: 2rpx solid #ddd;
  border-radius: 4rpx;
  font-size: 28rpx;
}


.input {
  width: 100%;
  height: 82rpx;
  font-size: 28rpx;
  line-height: 28rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  color: #333;
}

input-holder {
  color: #666;
  font-size: 28rpx;
}

.modal-footer {
  display: flex;
  flex-direction: row;
  height: 86rpx;
  border-top: 1px solid #dedede;
  font-size: 34rpx;
  line-height: 86rpx;
}

.btn-cancel {
  width: 50%;
  color: #666;
  text-align: center;
  border-right: 1px solid #dedede;
}

.btn-confirm {
  width: 50%;
  color: #CC3333;
  text-align: center;
}




.li1 {
  border-radius: 10rpx;
  height: 340rpx;
  transform: scale(0.9);
  width: 100%;
}

.selected {
  transform: scale(0.9)
}

.border_r {

  border-top-left-radius: 20px;

  border-top-right-radius: 20px;

  border-bottom-left-radius: 20px;

  border-bottom-right-radius: 20px;
}



.weui-tabbar_boo {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  position: fixed;
  z-index: 500;
  bottom: 0;
  width: 100%;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
}

.img-style {
  height: 0rpx;
  width: 0rpx;
  position: absolute;
  right: 50%;
  opacity: 0;
  bottom: 0px;
}

.img-plus-style {
  height: 100rpx;
  width: 100rpx;
  right: 42%;
  position: absolute;
  z-index: 100;
  bottom: 0px;
}


/*按钮大小  */
.audioOpen {
  width: 50rpx;
  height: 50rpx;
  border: 1px solid #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  margin-top: 57rpx;
}

.image2 {
  margin-left: 10%;
}

/*进度条长度  */
.slid {
  flex: 1;
  position: relative;
}

.slid view {
  display: flex;
  justify-content: space-between;
}

.slid view>text:nth-child(1) {
  color: #4c9dee;
  margin-left: 6rpx;
}

.slid view>text:nth-child(2) {
  margin-right: 6rpx;
}

slider {
  width: 520rpx;
  margin: 0;
  margin-left: 35rpx;
}

/*横向布局  */
.times {
  width: 100rpx;
  text-align: center;
  display: inline-block;
  font-size: 24rpx;
  color: #999999;
  margin-top: 5rpx;
}

.title view {
  text-indent: 2em;
}

.bf {
  font-weight: 600;
  background-image: linear-gradient(to top, #d5d4d0 0%, #d5d4d0 1%, #eeeeec 31%, #efeeec 75%, #e9e9e7 100%);
  color: transparent;
  -webkit-background-clip: text;
  animation: ran 20s linear infinite;
}

.flex {
  display: flex;
  align-items: flex-start;
}

#left,
#right {
  width: 48%;
  margin: 0 1%;
}
</style>