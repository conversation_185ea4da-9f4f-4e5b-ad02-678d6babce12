<template>
<view class="page-container">
<cu-custom bgColor="none" :isBack="true" :isSearch="false">
  <view slot="backText">返回</view>
  <view slot="content" style="color: #2c2b2b;font-weight: 600; font-size: 36rpx;">我的资料</view>
</cu-custom>
<view style="clear:both;height:0"></view>
<view class="content-wrapper">
  <!-- 头像区域 -->
  <view class="avatar-section">
    <view class="avatar-container">
      <view class="camera-icon" @tap="previewOneImage">📷</view>
      <image :src="img" class="avatar-image"></image>
      <view class="avatar-glow"></view>
    </view>
  </view>

  <!-- 昵称区域 -->
  <view class="form-card">
    <view class="cu-form-group">
      <view class="title">昵称</view>
      <input :disabled="info.is_nick_name" @input="get_input" :value="nick_name" maxlength="15" placeholder="请输入昵称"/>
    </view>
  </view>
  <!-- 昵称规则说明 -->
  <view class="tips-card">
    <view class="tips-title">昵称规则</view>
    <view class="tip-item">
      <text class="tip-number">1.</text>
      <text class="tip-text">昵称不超过 <text class="highlight">14</text> 个字</text>
    </view>
    <view class="tip-item">
      <text class="tip-number">2.</text>
      <text class="tip-text">昵称可以加emoji小表情 😊</text>
    </view>
    <view class="tip-item">
      <text class="tip-number">3.</text>
      <text class="tip-text">每 <text class="highlight">90天</text> 可以修改一次昵称，一定要谨慎哦</text>
    </view>
    <view class="tip-item">
      <text class="tip-number">4.</text>
      <text class="tip-text"><text class="highlight">会员</text> 最短只用等待 <text class="highlight">30天</text> 就可以改昵称</text>
    </view>
    <view v-if="info.is_nick_name" class="tip-item warning">
      <text class="tip-number">5.</text>
      <text class="tip-text"><text class="highlight">{{info.is_nick_name_end}}后</text> 可以修改昵称</text>
    </view>
  </view>

  <!-- 性别选择 -->
  <view class="form-card">
    <view class="card-title">性别选择</view>
    <radio-group class="gender-group">
      <view class="gender-option" @tap="handleFruitChange" data-sex="男">
        <view class="gender-content">
          <text class="gender-text">男</text>
        </view>
        <radio class="blue" :checked="current=='男'?true:false"></radio>
      </view>
      <view class="gender-option" @tap="handleFruitChange" data-sex="女">
        <view class="gender-content">
          <text class="gender-text">女</text>
        </view>
        <radio class="pink" :checked="current=='女'?true:false"></radio>
      </view>
    </radio-group>
  </view>
  <!-- 个人签名 -->
  <view class="form-card">
    <view class="card-title">个人签名</view>
    <view class="signature-container">
      <textarea
        :value="autograph"
        maxlength="140"
        :disabled="modalName!=null"
        @input="get_text"
        placeholder="写下你的个性签名吧~ (最多140个字)"
        class="signature-input">
      </textarea>
    </view>
  </view>

  <!-- 隐私设置 -->
  <view class="form-card">
    <view class="card-title">隐私设置</view>

    <view class="privacy-item">
      <view class="privacy-info">
        <view class="privacy-title"> 关闭主页</view>
        <view class="privacy-desc">开启主页访问权限(只能本人访问)</view>
      </view>
      <switch class="privacy-switch" @change="home_status" data-id="1" :checked="user_home_access_status"></switch>
    </view>

    <view class="privacy-item">
      <view class="privacy-info">
        <view class="privacy-title">开启粉丝隐私</view>
        <view class="privacy-desc">开启粉丝访问权限(只能本人访问)</view>
      </view>
      <switch class="privacy-switch" @change="home_status" data-id="2" :checked="is_enable_fans_privacy"></switch>
    </view>

    <view class="privacy-item">
      <view class="privacy-info">
        <view class="privacy-title">开启关注隐私</view>
        <view class="privacy-desc">开启关注访问权限(只能本人访问)</view>
      </view>
      <switch class="privacy-switch" @change="home_status" data-id="3" :checked="is_enable_concern_privacy"></switch>
    </view>
  </view>

  <!-- 保存按钮 -->
  <view class="save-button-container">
    <button @tap="edit_submit" class="save-button">
      <text class="save-text">保存资料</text>
    </button>
  </view>

  <!-- 上传加载提示 -->
  <view class="upload-modal" v-if="loadModal">
    <view class="upload-content">
      <text class="upload-icon">📤</text>
      <text class="upload-text">上传中...</text>
    </view>
  </view>
</view>
</view>
</template>

<script>
var app = getApp();
import http from "../../../util/http.js";
export default {
  /**
   * 页面的初始数据
   */
  data() {
    return {
      user_info: {},
      current: '男',
      img: '',
      nick_name: '',
      autograph: '',
      user_home_access_status: false,
      is_enable_fans_privacy: false,
      is_enable_concern_privacy: false,
      info: {},
      copyright: {},
      loadModal: false,
      modalName: null
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.copyright = getApp().globalData.store.getState().copyright;
    this.get_user_info();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    var forward = app.globalData.forward;
    console.log(forward);
    if (forward) {
      return {
        title: forward.title,
        path: '/yl_welore/pages/index/index',
        imageUrl: forward.reis_img
      };
    } else {
      return {
        title: '您的好友给您发了一条信息',
        path: '/yl_welore/pages/index/index'
      };
    }
  },

  methods: {
    home_status(d) {
      if (d.currentTarget.dataset.id == 1) {
        this.user_home_access_status = d.detail.value;
      }
      if (d.currentTarget.dataset.id == 2) {
        this.is_enable_fans_privacy = d.detail.value;
      }
      if (d.currentTarget.dataset.id == 3) {
        this.is_enable_concern_privacy = d.detail.value;
      }
    },

    /**
     * 昵称
     */
    get_input(e) {
      console.log(e);
      this.nick_name = e.detail.value;
    },
    /**
     * 个性签名
     */
    get_text(e) {
      this.autograph = e.detail.value;
    },
    /**
     * 编辑提交
     */
    edit_submit() {
      var b = app.globalData.api_root + 'User/edit_user_info';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.img = this.img;
      params.nick_name = this.nick_name;
      params.autograph = this.autograph;
      params.gender = this.current == '男' ? 1 : 2;
      params.user_home_access_status = this.user_home_access_status ? 0 : 1;
      params.is_enable_fans_privacy = this.is_enable_fans_privacy ? 1 : 0;
      params.is_enable_concern_privacy = this.is_enable_concern_privacy ? 1 : 0;
      http.POST(b, {
        params: params,
        success: function (res) {
          if (res.data.status == 'success') {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            that.get_user_info();
          } else {
            uni.showModal({
              title: '提示',
              content: res.data.msg,
              showCancel: false,
              success: function (res) {}
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) {}
          });
        }
      });
    },
    /**
     * 获取会员信息
     */
    get_user_info() {
      var b = app.globalData.api_root + 'User/get_user_info';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.status == 'success') {
            that.info = res.data.info;
            that.autograph = res.data.info.autograph;
            that.nick_name = res.data.info.user_nick_name;
            that.img = res.data.info.user_head_sculpture;
            that.current = res.data.info.gender == 1 || res.data.info.gender == 0 ? '男' : '女';
            that.user_home_access_status = res.data.info.user_home_access_status == 0 ? true : false;
            that.is_enable_fans_privacy = res.data.info.is_enable_fans_privacy == 1 ? true : false;
            that.is_enable_concern_privacy = res.data.info.is_enable_concern_privacy == 1 ? true : false;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) {}
          });
        }
      });
    },
    /**
     * 上传主图
     */
    previewOneImage() {
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var b = app.globalData.api_root + 'User/img_upload';
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ['album', 'camera'],
        // 可以指定来源是相册还是相机，默认二者都有
        success: function (res) {
          that.loadModal = true;
          var tempFilePaths = res.tempFilePaths;
          uni.uploadFile({
            url: b,
            filePath: tempFilePaths[0],
            name: 'sngpic',
            header: {
              "content-type": "multipart/form-data"
            },
            formData: {
              "content-type": "multipart/form-data",
              'token': e.token,
              'openid': e.openid,
              'much_id': app.globalData.siteInfo.uniacid
            },
            success: function (res) {
              console.log(res);
              var data = JSON.parse(res.data);
              console.log(data);
              if (data.status == 'error') {
                uni.showModal({
                  title: '提示',
                  content: data.msg
                });
              } else {
                that.img = data.url;
              }
              that.loadModal = false;
            },
            fail: function (res) {
              uni.showToast({
                title: '上传错误！',
                icon: 'none',
                duration: 2000
              });
            }
          });
        }
      });
    },
    handleFruitChange(detail) {
      console.log(detail);
      this.current = detail.currentTarget.dataset.sex;
    }
  }
}
</script>
<style>
/* 页面整体样式 */
page {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  min-height: 100vh;
}

.page-container {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  min-height: 100vh;
}

.content-wrapper {
  padding: 30rpx;
  padding-bottom: 150rpx;
}

/* 头像区域样式 */
.avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
}

.avatar-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 2;
}

.avatar-glow {
  position: absolute;
  width: 90px;
  height: 90px;
  border-radius: 50%;
  background: linear-gradient(45deg, #a8e6cf, #88d8c0, #7fcdcd, #81c784);
  animation: rotate 4s linear infinite;
  z-index: 1;
}

.camera-icon {
  position: absolute;
  right: -5px;
  bottom: 5px;
  width: 35px;
  height: 35px;
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  z-index: 3;
  cursor: pointer;
}

.camera-icon:hover {
  transform: scale(1.1);
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 卡片样式 */
.form-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

/* 表单组样式 */
.cu-form-group {
  background: transparent;
  border: none;
  padding: 20rpx 0;
  min-height: 80rpx;
}

.cu-form-group .title {
  font-size: 30rpx !important;
  font-weight: 500;
  color: #555;
  display: flex;
  align-items: center;
}

.cu-form-group input {
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12rpx;
  padding-left: 20rpx;
  border: 1px solid rgba(0, 0, 0, 0.1);
  height: 80rpx;
}

.cu-form-group input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 提示卡片样式 */
.tips-card {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
}

.tips-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3436;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15rpx;
  padding: 10rpx 0;
}

.tip-number {
  color: #e17055;
  font-weight: 600;
  margin-right: 10rpx;
  min-width: 30rpx;
}

.tip-text {
  color: #2d3436;
  font-size: 26rpx;
  line-height: 1.5;
  flex: 1;
}

.highlight {
  color: #e17055;
  font-weight: 600;
}

.warning {
  background: rgba(231, 76, 60, 0.1);
  border-radius: 10rpx;
  padding: 15rpx;
  margin-top: 10rpx;
}

/* 性别选择样式 */
.gender-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.gender-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 15rpx;
  border: 2px solid transparent;
  cursor: pointer;
}

.gender-option:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.gender-content {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.gender-icon {
  font-size: 40rpx;
}

.gender-text {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

/* 个人签名样式 */
.signature-container {
  background: rgba(248, 249, 250, 0.8);
  border-radius: 15rpx;
  padding: 20rpx;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.signature-input {
  width: 100%;
  min-height: 120rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  background: transparent;
  border: none;
  resize: none;
}

.signature-input::placeholder {
  color: #999;
}

/* 隐私设置样式 */
.privacy-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.privacy-item:last-child {
  border-bottom: none;
}

.privacy-info {
  flex: 1;
}

.privacy-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  display: flex;
  align-items: center;
}

.privacy-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.privacy-switch {
  transform: scale(0.8);
}

/* 保存按钮样式 */
.save-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.95), transparent);
  backdrop-filter: blur(10px);
}

.save-button {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  border-radius: 50rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  box-shadow: 0 4px 20px rgba(116, 185, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.save-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.save-button:hover::before {
  left: 100%;
}

.save-button:active {
  transform: scale(0.98);
}

.save-icon {
  font-size: 36rpx;
}

.save-text {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}

/* 上传加载样式 */
.upload-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}

.upload-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 60rpx 80rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.upload-icon {
  font-size: 60rpx;
  animation: bounce 1s infinite;
}

.upload-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .content-wrapper {
    padding: 20rpx;
  }

  .form-card {
    padding: 20rpx;
  }

  .avatar-image {
    width: 70px;
    height: 70px;
  }

  .avatar-glow {
    width: 80px;
    height: 80px;
  }
}
</style>