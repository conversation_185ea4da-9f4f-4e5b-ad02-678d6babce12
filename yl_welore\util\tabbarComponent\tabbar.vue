<template>
  <view>
    <view class="weui-tabbar_boo" :animation="animBack" style="height:55px;z-index:250">

      <view @tap="nav_add" data-k="tuwen" :class="'img-style ' + ($state.isIphoneX ? 'add-height' : '')"
        :animation="animInput">
        <image style="width:100%;height:100%;" :src="$state.diy.pattern_data.release.list['writing']['images']"></image>
        <view style="text-align:center;margin-top:-5px;font-size:12px;">
          {{ $state.diy.pattern_data.release.list['writing']['title'] }}</view>
      </view>
      <view @tap="nav_add" data-k="shipin" :class="'img-style ' + ($state.isIphoneX ? 'add-height' : '')"
        :animation="animationM">
        <image v-if="$state.diy.user_vip.video_member == 1"
          style="width:40px;height:40px;position:absolute;left:0;z-index: 10;"
          src="/static/yl_welore/style/icon/left_vip.png"></image>
        <image style="width:100%;height:100%;" :src="$state.diy.pattern_data.release.list['video']['images']"></image>
        <view style="text-align:center;margin-top:-5px;font-size:12px;">
          {{ $state.diy.pattern_data.release.list['video']['title'] }}</view>
      </view>
      <view @tap="nav_add" data-k="yuyin" :class="'img-style ' + ($state.isIphoneX ? 'add-height' : '')"
        :animation="animTranspond">
        <image v-if="$state.diy.user_vip.voice_member == 1"
          style="width:40px;height:40px;position:absolute;left:0;z-index: 10;"
          src="/static/yl_welore/style/icon/left_vip.png"></image>
        <image style="width:100%;height:100%;" :src="$state.diy.pattern_data.release.list['audio']['images']"></image>
        <view style="text-align:center;font-size:12px;margin-top:-5px">
          {{ $state.diy.pattern_data.release.list['audio']['title'] }}</view>
      </view>

      <view @tap="nav_add" data-k="toupiao" :class="'img-style ' + ($state.isIphoneX ? 'add-height' : '')"
        :animation="animCollect">
        <image v-if="$state.diy.user_vip.vote_member == 1"
          style="width:40px;height:40px;position:absolute;left:0;z-index: 10;"
          src="/static/yl_welore/style/icon/left_vip.png"></image>
        <image style="width:100%;height:100%;" :src="$state.diy.pattern_data.release.list['graffito']['images']">
        </image>
        <view style="text-align:center;margin-top:-5px;font-size:12px;">
          {{ $state.diy.pattern_data.release.list['graffito']['title'] }}</view>
      </view>



      <view @tap="nav_add" data-k="huodong" :class="'img-style ' + ($state.isIphoneX ? 'add-height' : '')"
        :animation="animationHD">
        <image v-if="$state.diy.user_vip.brisk_member == 1"
          style="width:40px;height:40px;position:absolute;left:0;z-index: 10;"
          src="/static/yl_welore/style/icon/left_vip.png"></image>
        <image style="width:100%;height:100%;" :src="$state.diy.pattern_data.release.list['brisk']['images']"></image>
        <view style="text-align:center;margin-top:-5px;font-size:12px;">
          {{ $state.diy.pattern_data.release.list['brisk']['title'] }}</view>
      </view>
    </view>

    <view :class="'tabbar_box ' + ($state.isIphoneX ? 'iphoneX-height' : '')"
      :style="'background-color:' + (tabbar.backgroundColor)">
      <block v-for="(item, index) in (tabbar.list)" :key="index">




        <view @tap="plus" v-if="item.isSpecial == true && $state.copyright.home_release_arbor == 1" class="tabbar_nav"
          :style="'color:' + (isPopping ? tabbar.selectedColor : tabbar.color)">
          <view class="special-wrapper">
            <image class="tabbar_icon" :src="item.iconPath"></image>
          </view>
          <image class="special-text-wrapper"></image>
          <text>{{ item.text }}</text>
        </view>








        <view @tap="open_url" v-if="item.isSpecial == false && !item.selected" class="tabbar_nav"
          :data-home="item.home_s" :data-url="item.pagePath">
          <button :style="'color:' + (item.selected && isPopping == false ? tabbar.selectedColor : tabbar.color)"
            class="i-tab-bar-item" form-type="submit" hover-class="none">
            <block v-if="floorstatus && index == 0">
              <image class="tabbar_icon" :src="item.selected ? item.selectedIconPath : item.iconPath"></image>
              <text>{{ item.text }}</text>
            </block>
            <block v-else>
              <image class="tabbar_icon" :src="item.selected ? item.selectedIconPath : item.iconPath"></image>
              <text>{{ item.text }}</text>
            </block>
          </button>
          <view
            v-if="$state.msg_count > 0 && index == 3 && ($state.diy.shop_arbor == 0 || $state.diy.elect_sheathe == 1)"
            class="cu-tag badge"
            style="right: 15rpx;border-radius: 50%;font-size: 24rpx;padding: 10rpx;height: 35rpx;width: 35rpx;">
            {{ $state.msg_count }}</view>
        </view>
        <!-- 点击态 -->

        <view v-if="item.isSpecial == false && item.selected" class="tabbar_nav">
          <button :style="'color:' + (item.selected && isPopping == false ? tabbar.selectedColor : tabbar.color)"
            class="i-tab-bar-item" form-type="submit" hover-class="none">
            <block v-if="floorstatus && index == 0">
              <image @tap.stop.prevent="goTop" class="tabbar_icon animation-slide-bottom"
                :src="(http_root) + 'addons/yl_welore/web/static/applet_icon/tag_top.png'"></image>
              <text style="color: #333333;" class="animation-slide-bottom">返回顶部</text>
            </block>
            <block v-else>
              <image class="tabbar_icon animation-slide-bottom"
                :src="item.selected ? item.selectedIconPath : item.iconPath"></image>
              <text class="animation-slide-bottom">{{ item.text }}</text>
            </block>
          </button>
          <view
            v-if="$state.msg_count > 0 && index == 3 && ($state.diy.shop_arbor == 0 || $state.diy.elect_sheathe == 1)"
            class="cu-tag badge"
            style="right: 15rpx;border-radius: 50%;font-size: 24rpx;padding: 10rpx;height: 35rpx;width: 35rpx;">
            {{ $state.msg_count }}</view>
        </view>
        <!-- 点击态 -->
      </block>
    </view>


    <login id="tabbar" :check_user_login="check_user_login"></login>
    <phone id="tabbar1" @close_phone_modal="closePhoneModal" :check_phone="check_phone"></phone>
  </view>
</template>

<script>
import login from "@/yl_welore/util/user_login/login";
import phone from "@/yl_welore/util/user_phone/phone";
var http = require("@/yl_welore/util/http.js");

var app = getApp();
var timer = null;

export default {
  name: "tabbar",
  components: {
    login,
    phone
  },
  props: {
    tabbar: {
      type: Object,
      default: () => ({})
    },
    floorstatus: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      http_root: app.globalData.http_root,
      isIphoneX: app.globalData.isIpx,
      isPopping: false,
      animCollect1: {},
      animCollect: {},
      animTranspond: {},
      animInput: {},
      animBack: {},
      animationHD: {},
      animationM: {},
      diy: {},
      user_info: '',
      copyright: {},
      version: 0,
      check_user_login: false,
      check_phone: false
    };
  },
  mounted() {
    setTimeout(() => {
      this.get_user_info();
    }, 2000);
    this.handlePageShow();
  },
  beforeDestroy() {
    this.handlePageHide();
  },
  methods: {
    closePhoneModal() {
      this.check_phone = false;
    },
    handlePageShow() {
      clearInterval(timer);
      const copyright = app.globalData.store.getState().copyright;
      const diy = app.globalData.store.getState().diy;
      if (JSON.stringify(diy) === "{}") {
        return;
      }
      if (copyright['speech_arbor'] == 0 && diy['elect_sheathe'] == 1) {
        return;
      }
      this.isPopping = false;
      this.takeback();
      timer = setInterval(() => {
        this.GetOverallSituation();
      }, 5000);
    },
    handlePageHide() {
      clearInterval(timer);
    },
    goTop: function (e) {
      if (uni.pageScrollTo) {
        uni.pageScrollTo({
          selector: "#top_order",
          offsetTop: -100
        });
      } else {
        uni.showModal({
          title: '提示',
          content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'
        });
      }
    },
    GetOverallSituation() {
      var b = app.globalData.api_root + 'Conversation/get_overall_situation';
      var e = app.globalData.getCache("userinfo");
      if (!e) {
        return;
      }
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: function (res) {
          app.globalData.store.setState({
            msg_count: res.data.count
          });
        }
      });
    },
    open_url: function (d) {
      var home = d.currentTarget.dataset.home;
      var url = d.currentTarget.dataset.url;
      if (home == 1) {
        uni.navigateTo({
          url: url
        }); 
      } else {
        uni.switchTab({
          url: url
        });
      }
    },
    get_diy: function () {
      var b = app.globalData.api_root + 'User/get_diy';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.uid = e.uid;
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.status) {
            return;
          } else {
            that.version = res.data.version;
            that.diy = res.data;
            that.add = res.data.pattern_data.release.list;
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    get_user_info: function () {
      var b = app.globalData.api_root + 'User/get_user_info';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log('user_info', res);
          if (res.data.status == 'success') {
            that.user_info = res.data.info;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    preventTouchMove: function () { },
    plus: function () {
      var op = uni.getLaunchOptionsSync();
      if (op.scene == 1154) {
        return;
      }
      if (this.user_info == '' || !this.user_info) {
        this.get_user_info();
        return;
      }
      if (this.isPopping == false) {
        this.popp();
        this.isPopping = true;
        this.home_current = 'add';
      } else if (this.isPopping == true) {
        this.takeback();
        this.isPopping = false;
        this.home_current = 'home';
      }
    },
    popp: function () {
      var that = this;
      var animationcollect = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationcollect1 = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationTranspond = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationInput = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animBackCollect = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationM = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationHD = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var copyright = getApp().globalData.store.getState().copyright;
      var myCars = new Array();
      myCars[0] = [[25, -105], [25, -105]];
      myCars[1] = [[-50, -105], [100, -105]];
      myCars[2] = [[-85, -105], [30, -105], [140, -105]];
      myCars[3] = [[-100, -105], [-15, -105], [75, -105], [155, -105]];
      myCars[4] = [[-125, -105], [25, -105], [100, -105], [-50, -105], [170, -105]];
      var tab = [animationInput];
      if (copyright['version'] == 1) {
      } else {
        if (copyright['hair_audio_arbor'] == 1) {
          tab.push(animationTranspond);
        }
        if (copyright['hair_vote_arbor'] == 1) {
          tab.push(animationcollect);
        }
        if (copyright['hair_video_arbor'] == 1) {
          tab.push(animationM);
        }
        if (copyright['hair_brisk_arbor'] == 1) {
          tab.push(animationHD);
        }
      }
      console.log('tab', tab);
      var key = tab.length - 1;
      for (var i = 0; i < tab.length; i++) {
        tab[i].translate(myCars[key][i][0], myCars[key][i][1]).opacity(1).height('100rpx').width('100rpx').step();
      }
      animBackCollect.backgroundColor('#F7F9FA').height(190).step();

      this.animCollect = animationcollect.export();
      this.animTranspond = animationTranspond.export();
      this.animInput = animationInput.export();
      this.animationM = animationM.export();
      this.animBack = animBackCollect.export();
      this.animationHD = animationHD.export();
    },
    takeback: function () {
      var animationPlus = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationcollect = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationTranspond = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationInput = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animBackCollect = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationM = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationHD = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      animationPlus.rotateZ(0).step();
      animationcollect.translate(0, 0).rotateZ(0).opacity(0).height('0rpx').width('0rpx').step();
      animationTranspond.translate(0, 0).rotateZ(0).opacity(0).height('0rpx').width('0rpx').step();
      animationInput.translate(0, 0).rotateZ(0).opacity(0).height('0rpx').width('0rpx').step();
      animationM.translate(0, 0).rotateZ(0).opacity(0).height('0rpx').width('0rpx').step();
      animationHD.translate(0, 0).rotateZ(0).opacity(0).height('0rpx').width('0rpx').step();
      animBackCollect.backgroundColor('transparent').height(45).step();

      this.animCollect = animationcollect.export();
      this.animTranspond = animationTranspond.export();
      this.animInput = animationInput.export();
      this.animationM = animationM.export();
      this.animationHD = animationHD.export();
      this.animBack = animBackCollect.export();
    },
    nav_add: function (d) {
      var op = uni.getLaunchOptionsSync();
      if (op.scene == 1154) {
        return;
      }
      var user_info = this.user_info;
      console.log('tabbar_user_info', user_info);
      if (app.globalData.__CheckTheCertification(user_info)) {
        return;
      }
      var e = app.globalData.getCache("userinfo");
      if (e.tourist == 1) {
        this.check_user_login = true;
        return;
      }
      var k = d.currentTarget.dataset.k;
      var copyright = getApp().globalData.store.getState().copyright;
      if (copyright['force_phone_arbor'] == 1 && !e['user_phone'] && copyright['version'] == 0) {
        this.check_login();
        return;
      }
      
      if (k == 'tuwen') {
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/add/index?type=0&fa_class=0&name=&gambit_name=&gambit_id=0'
        });
      }
      if (k == 'toupiao') {
        if (copyright['vote_member'] == 1) {
          if (user_info['is_vip'] == 1) {
            uni.navigateTo({
              url: '/yl_welore/pages/packageA/add/index?type=6&fa_class=0&name=&gambit_name=&gambit_id=0'
            });
          } else {
            uni.showToast({
              title: '此功能仅限VIP用户使用',
              icon: 'none',
              duration: 2000
            });
            return;
          }
        } else {
          uni.navigateTo({
            url: '/yl_welore/pages/packageA/add/index?type=6&fa_class=0&name=&gambit_name=&gambit_id=0'
          });
        }
      }
      if (k == 'yuyin') {
        if (copyright['voice_member'] == 1) {
          if (user_info['is_vip'] == 1) {
            uni.navigateTo({
              url: '/yl_welore/pages/packageA/add/index?type=1&fa_class=0&name=&gambit_name=&gambit_id=0'
            });
          } else {
            uni.showToast({
              title: '此功能仅限VIP用户使用',
              icon: 'none',
              duration: 2000
            });
            return;
          }
        } else {
          uni.navigateTo({
            url: '/yl_welore/pages/packageA/add/index?type=1&fa_class=0&name=&gambit_name=&gambit_id=0'
          });
        }
      }
      if (k == 'shipin') {
        if (copyright['video_member'] == 1) {
          if (user_info['is_vip'] == 1) {
            uni.navigateTo({
              url: '/yl_welore/pages/packageA/add/index?type=2&fa_class=0&name=&gambit_name=&gambit_id=0'
            });
          } else {
            uni.showToast({
              title: '此功能仅限VIP用户使用',
              icon: 'none',
              duration: 2000
            });
            return;
          }
        } else {
          uni.navigateTo({
            url: '/yl_welore/pages/packageA/add/index?type=2&fa_class=0&name=&gambit_name=&gambit_id=0'
          });
        }
      }
      if (k == 'huodong') {
        if (copyright['brisk_member'] == 1) {
          if (user_info['is_vip'] == 1) {
            uni.navigateTo({
              url: '/yl_welore/pages/packageA/add/index?type=4&fa_class=0&name=&gambit_name=&gambit_id=0'
            });
          } else {
            uni.showToast({
              title: '此功能仅限VIP用户使用',
              icon: 'none',
              duration: 2000
            });
            return;
          }
        } else {
          uni.navigateTo({
            url: '/yl_welore/pages/packageA/add/index?type=4&fa_class=0&name=&gambit_name=&gambit_id=0'
          });
        }
      }
    },
    check_login: function () {
      var n = this;
      uni.checkSession({
        success() {
          console.log(1);
          n.check_phone = true;
        },
        fail() {
          console.log(2);
          n.check_phone = false;
          uni.showModal({
            title: '提示',
            content: '您的登录状态已过期，点击确定更新！',
            showCancel: false,
            success(res) {
              n.do_login();
            }
          });
        }
      });
    },
    do_login: function () {
      var n = this;
      uni.login({
        success: function (o) {
          var b = app.globalData.api_root + 'Login/index';
          var params = new Object();
          params.code = o.code;
          http.POST(b, {
            params: params,
            success: function (res) {
              console.log(res);
              if (res.data.code == 0) {
                if (!res.data.info.errcode) {
                  var e = app.globalData.getCache("userinfo");
                  e.session_key = res.data.info.session_key;
                  console.log(e);
                  app.globalData.setCache("userinfo", e);
                  n.check_phone = true;
                } else {
                  uni.showModal({
                    title: '提示',
                    content: '获取用户信息失败',
                    showCancel: false
                  });
                  return false;
                }
              } else {
                uni.showModal({
                  title: '提示',
                  content: '获取用户信息失败',
                  showCancel: false
                });
              }
            }
          });
        }
      });
    }
  }
};
</script>
<style>
.tabbar_box {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 800;
  width: 100%;
  height: 98rpx;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
}

.add-height {
  margin-bottom: 28rpx;
}

.iphoneX-height {
  padding-bottom: 55rpx;
  padding-top: 10px;
  height: 160rpx;
}

.iphoneX-zhuan {
  padding-bottom: 35px;
  height: 90px;
}

.img-style {
  height: 0rpx;
  width: 0rpx;
  position: absolute;
  right: 50%;
  opacity: 0;
  bottom: 0px;
}

.weui-tabbar_boo {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  position: fixed;
  bottom: 0;
  width: 100%;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
}

.middle-wrapper {
  position: absolute;
  right: 310rpx;
  bottom: 0;
  background-color: #fff;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border-top: 2rpx solid #f2f2f3;
}

.tabbar_nav {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 20rpx;
  height: 100%;
  position: relative;
}

.tabbar_icon {
  width: 56rpx;
  height: 56rpx;
}

.special-wrapper {
  position: absolute;
  left: 30rpx;
  top: -36rpx;
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  border-top: 2rpx solid #f2f2f3;
  background-color: #fff;
  text-align: center;
  box-sizing: border-box;
  padding: 6rpx;
}

.special-wrapper .tabbar_icon {
  width: 84rpx;
  height: 84rpx;
}

.special-text-wrapper {
  width: 56rpx;
  height: 56rpx;
}

.i-tab-bar-item {
  flex: 1;
  display: flex;
  width: 100%;
  -webkit-box-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  flex-direction: column;
  text-align: center;
  font-size: 20rpx;
}

button::after {
  line-height: normal;
  font-size: 30rpx;
  width: 0;
  height: 0;
  top: 0;
  left: 0;
}

button {
  line-height: normal;
  display: block;
  padding-left: 0px;
  padding-right: 0px;
  background-color: rgba(255, 255, 255, 0);
  font-size: 30rpx;
}
</style>