<template>
  <view>
    <cu-custom bgColor="bg-white" :isBack="true" :isSearch="false">
      <view slot="backText">返回</view>
      <view slot="content" style="color: #2c2b2b;font-weight: 600; font-size: 36rpx;">搜一搜</view>
    </cu-custom>

    <view class="cu-bar search bg-white">
      <view class="search-form round">
        <text class="cuIcon-search"></text>
        <input type="text" @confirm="submit_search" :value="get_search" @input="get_search_do" placeholder="搜索图片、文章、用户"
          confirm-type="search" />
      </view>
      <view v-if="(!set_list)" class="action">
        <button @tap="submit_search" class="cu-btn bg-green shadow-blur round">搜索</button>
      </view>
      <view v-if="set_list" @tap="col_search" class="action">
        <text class="cuIcon-close"></text>
        <text>取消</text>
      </view>
    </view>
    <view class="bg-white padding" v-if="is_search && set_list == false">
      <view style="font-weight:700;">
        <view style="float:left">搜索历史</view>
        <view style="float:right;line-height:33px;" @tap="removeStorage">
          <image src="/static/yl_welore/style/icon/laji.png" style="width:20px;height:20px;"></image>
        </view>
        <view style="clear:both;height:0"></view>
      </view>
      <view class="bg-white">
        <view class="cf">
          <view @tap="lishi_search" :data-search="item.search" style="background-color:#F6F6F6;height: 40px;"
            class="fl padding-sm margin-xs" v-for="(item, index) in (get_lishi)" :key="index">{{ item.search }}</view>
        </view>
      </view>
    </view>
    <view style="clear:both;height:0"></view>
    <view class="bg-white" style="padding:20px;" v-if="set_list">
      <view style="font-weight:700;">
        <view>用户</view>
      </view>
      <view style="width:100%;margin-top:10px;" v-if="user_search_yes == 0">
        <view style="float:left;font-size:14px;">暂时没有《{{ get_search }}》的用户</view>
      </view>
      <view style="clear:both;height:0"></view>
      <view class="cu-list menu-avatar">
        <view class="cu-item" v-for="(item, index) in (user)" :key="index">
          <view class="cu-avatar round lg" :style="'background-image:url(' + (item.user_head_sculpture) + ');'"></view>
          <view class="content flex-sub">
            <view>{{ item.user_nick_name }}</view>
            <view class="text-gray text-sm flex justify-between">
              粉丝{{ item.user_fs }}人　|　{{ item.user_paper }}条内容
            </view>
          </view>
          <view class="text-gray text-sm flex justify-end">
            <view @tap="home_url" data-k="4" :data-id="item.id"
              style="color:#ffffff;font-size:12px;background-color:#00CC33;padding:10rpx;width:100rpx;text-align:center;border-radius:3px;z-index:100;">
              去主页</view>
          </view>
        </view>
      </view>
    </view>
    <view style="clear:both;height:0"></view>
    <view class="bg-white" style="padding:20px;" v-if="set_list">
      <view style="font-weight:700;">
        <view>{{ design.landgrave }}</view>
      </view>
      <view style="width:100%;margin-top:10px;" v-if="is_search_yes == 0">
        <view style="float:left;font-size:14px;">暂时没有"{{ get_search }}"的{{ design.landgrave }}</view>
      </view>
      <view style="clear:both;height:0"></view>

      <view class="cu-list menu-avatar">
        <view class="cu-item" v-for="(item, index) in (territory)" :key="index">
          <view class="cu-avatar round lg" :style="'background-image:url(' + (item.realm_icon) + ');'"></view>
          <view class="content flex-sub">
            <view>{{ item.realm_name }}</view>
            <view class="text-gray text-sm flex justify-between">
              {{ item.concern }}人加入　|　{{ item.paper_count }}条内容
            </view>
          </view>
          <view class="text-gray text-sm flex justify-end">
            <view @tap="home_url" data-k="2" :data-id="item.id"
              style="color:#ffffff;font-size:12px;background-color:#00CC33;padding:10rpx;width:100rpx;text-align:center;border-radius:3px;z-index:100;">
              去{{ design.landgrave }}</view>
          </view>
        </view>
      </view>
    </view>

    <view style="background-color:#ffffff;" v-if="set_list">
      <view style="width:100%;height:5px;background-color:#F7F7FA;"></view>
      <view style="font-weight:700;margin:20px;">
        <view>动态</view>
      </view>
      <Index1 v-if="mod.home == 0" :data="this"></Index1>
      <Index2 v-if="mod.home == 'aa3b1c88-2d41-9cde-cff7-55372169e4eb'" :data="this"></Index2>
      <Index3 v-if="mod.home == 'be454a15-e373-f773-376b-127f3a35d3c6'" :data="this"></Index3>
      <Index4 v-if="mod.home == '453776a4-6724-fd4f-4ff1-48363b245915'" :data="this"></Index4>
      <Index5 v-if="mod.home == 'd5b2d78e-3152-ee54-aca8-a4402adc601b'" :data="this"></Index5>
      <Index6 v-if="mod.home == '58567383-612e-ca8f-116d-89e1057eb02a'" :data="this"></Index6>
      <Index7 v-if="mod.home == '9701db92-a7e1-bdd7-842e-9e9bea168127'" :data="this"></Index7>
      <Index8 v-if="mod.home == '47a436a1-6541-a7a0-5723-61d7fe40b7c3'" :data="this"></Index8>
      <Index9 v-if="mod.home == '57228047-ad66-b5c0-2970-0be62de79377'" :data="this"></Index9>
    </view>

    <!-- <i-modal title="清空" visible="{{clean}}" bind:ok="Close_ok" bind:cancel="Close_no">
  <view>确定要清空搜索记录吗？</view>
</i-modal> -->

    <view :class="'cu-modal ' + (clean ? 'show' : '')">
      <view class="cu-dialog">
        <view class="cu-bar bg-white justify-end">
          <view class="content">提示</view>
          <view class="action" @tap="Close_no">
            <text class="cuIcon-close text-red"></text>
          </view>
        </view>
        <view class="padding-xl">
          确定要清空搜索记录吗？
        </view>
        <view class="cu-bar bg-white justify-end">
          <view class="action">
            <button class="cu-btn line-green text-green" @tap="Close_no">取消</button>
            <button class="cu-btn bg-green margin-left" @tap="Close_ok">确定</button>

          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import Index1 from "../../index/index1.vue";
import Index2 from "../../index/index2.vue";
import Index3 from "../../index/index3.vue";
import Index4 from "../../index/index4.vue";
import Index5 from "../../index/index5.vue";
import Index6 from "../../index/index6.vue";
import Index7 from "../../index/index7.vue";
import Index8 from "../../index/index8.vue";
import Index9 from "../../index/index9.vue";
import http from "../../../util/http.js";
const innerAudioContext = uni.getBackgroundAudioManager();

export default {
  components: {
    Index1,
    Index2,
    Index3,
    Index4,
    Index5,
    Index6,
    Index7,
    Index8,
    Index9
  },
  data() {
    return {
      http_root: getApp().globalData.http_root,
      new_list: [],
      page: 1,
      di_msg: false,
      get_search: '',
      show: true,
      clean: false,
      set_list: false,
      territory: [],
      user: [],
      current: 'tab1',
      mod: {},
      images: [],
      dian_index: -1,
      version: 0,
      design: {},
      copyright: {},
      is_search: false,
      get_lishi: [],
      is_search_yes: null,
      user_search_yes: null,
      new_list_index: -1,
      animation_zan: null,
    };
  },

  onLoad(options) {
    const design = uni.getStorageSync('is_diy');
    this.design = design;
    console.log(this.copyright);
  },

  onShow() {
    uni.hideShareMenu();
    this.get_diy();
    if (this.show === false) {
      return;
    }
    const s = getApp().globalData.getCache("search");
    console.log(s);
    if (s) {
      this.is_search = true;
      this.get_arr_dx(s);
    } else {
      this.is_search = false;
    }
  },

  onReachBottom() {
    this.page = this.page + 1;
    this.get_index_list();
  },

  onShareAppMessage(d) {
    const forward = getApp().globalData.forward;
    const key = d.target.dataset.key;
    const info = this.new_list[key];
    if (forward) {
      return {
        title: forward.title,
        path: `/yl_welore/pages/packageA/article/index?id=${info.id}&type=${info.info_type}`,
        imageUrl: forward.reis_img
      };
    } else {
      let img;
      if (info.image_part.length > 0) {
        img = info.image_part[0];
      }
      return {
        title: info.study_title === '' ? info.study_content : info.study_title,
        path: `/yl_welore/pages/packageA/article/index?id=${info.id}&type=${info.info_type}`,
        imageUrl: img
      };
    }
  },

  methods: {
    by_url(item) {
      const info = this.new_list[item.target.dataset.k];
      const videoContext = uni.createVideoContext('myVideo' + info['id']);
      if (info['check_look'] == 0 && info['is_buy'] == 1) {
        videoContext.stop();
        uni.navigateTo({
          url: `/yl_welore/pages/packageA/article/index?id=${info['id']}&type=${info['study_type']}`
        });
      }
    },

    vote_do(item) {
      const index = this.dian_index;
      const key = item.currentTarget.dataset.key;
      if (this.new_list[key].vo_id.length === 0) {
        uni.showToast({
          title: '请选择选项',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      const that = this;
      const e = getApp().globalData.getCache("userinfo");
      const params = {};
      params.token = e.token;
      params.openid = e.openid;
      params.paper_id = this.new_list[key].id;
      params.vo_id = this.agree(this.new_list[key].vo_id);
      const b = getApp().globalData.api_root + 'Polls/vote_do';
      http.POST(b, {
        params: params,
        success(res) {
          console.log(res);
          if (res.data.status === "success") {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            that.$set(that.new_list[key], 'is_vo_check', 1);
            that.$set(that.new_list[key], 'vo_count', that.new_list[key].vo_count + 1);
            that.$set(that.new_list[key].vo[index], 'voters', that.new_list[key].vo[index].voters + 1);
          } else {
            uni.showModal({
              title: '提示',
              content: res.data.msg,
              showCancel: false
            });
          }
        },
        fail() {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
          });
        }
      });
    },

    agree(rows) {
      const ids = [];
      for (let i = 0; i < rows.length; i++) {
        const signAgainReq = {};
        signAgainReq.pv_id = rows[i];
        ids.push(signAgainReq);
      }
      return JSON.stringify(ids);
    },

    dian_option(item) {
      const index = item.currentTarget.dataset.index;
      this.dian_index = index;
      const key = item.currentTarget.dataset.key;
      const info = this.new_list[key];
      if (info.is_vo_check > 0) {
        return;
      }
      const vo_id = item.currentTarget.dataset.id;
      const vo_id_list = this.new_list[key].vo_id;

      if (info['study_type'] == 4) { // 单选
        if (vo_id == info['vo_id'][0]) {
          this.$set(this.new_list[key], 'vo_id', []);
          return;
        }
        this.$set(this.new_list[key], 'vo_id', [vo_id]);
      } else { // 多选
        const vo_id_index = vo_id_list.indexOf(vo_id);
        if (vo_id_index !== -1) {
          info.vo_id.splice(vo_id_index, 1);
        } else {
          info.vo_id.push(vo_id);
        }
        this.$set(this.new_list[key], 'vo_id', info.vo_id);
      }
    },

    home_url(dd) {
      console.log(dd);
      const key = dd.currentTarget.dataset.k;
      const e = getApp().globalData.getCache("userinfo");
      switch (key) {
        case 1:
          if (dd.currentTarget.dataset.user_id == 0) {
            uni.showToast({
              title: '身份已隐藏',
              icon: 'none',
              duration: 2000
            });
            return;
          }
          uni.navigateTo({
            url: `/yl_welore/pages/packageB/my_home/index?id=${dd.currentTarget.dataset.user_id}`
          });
          break;
        case 2:
          uni.navigateTo({
            url: `/yl_welore/pages/packageA/circle_info/index?id=${dd.currentTarget.dataset.id}`
          });
          break;
        case 3:
          const douyin = getApp().globalData.__PlugUnitScreen('5fb4baf1f25fe251685b526dc8c30b8f');
          const info = this.new_list[dd.currentTarget.dataset.index];
          if (dd.currentTarget.dataset.type == 2 && info.is_buy == 0 && e.user_phone && douyin) {
            uni.navigateTo({
              url: `/yl_welore/pages/packageF/full_video/index?id=${dd.currentTarget.dataset.id}`
            });
          } else {
            uni.navigateTo({
              url: `/yl_welore/pages/packageA/article/index?id=${dd.currentTarget.dataset.id}&type=${dd.currentTarget.dataset.type}`
            });
          }
          break;
        case 4:
          uni.navigateTo({
            url: `/yl_welore/pages/packageB/my_home/index?id=${dd.currentTarget.dataset.id}`
          });
          break;
      }
    },

    gambit_list(d) {
      const id = d.currentTarget.dataset.id;
      uni.navigateTo({
        url: `/yl_welore/pages/gambit/index?id=${id}`
      });
    },

    get_diy() {
      const b = getApp().globalData.api_root + 'User/get_diy';
      const that = this;
      const e = getApp().globalData.getCache("userinfo");
      const params = {};
      params.uid = e.uid;
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success(res) {
          that.mod = res.data.mod;
        },
        fail() {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
          });
        }
      });
    },

    get_search_do(e) {
      const text = e.detail.value;
      console.log(text);
      this.get_search = text;
      if (text === '') {
        this.set_list = false;
      }
    },

    col_search() {
      this.get_search = '';
      this.set_list = false;
    },

    lishi_search(a) {
      this.get_search = a.currentTarget.dataset.search;
      this.page = 1;
      this.new_list = [];
      this.get_index_list();
    },

    removeStorage() {
      this.clean = true;
    },

    Close_no() {
      this.clean = false;
    },

    Close_ok() {
      this.clean = false;
      this.get_lishi = [];
      this.is_search = false;
      getApp().globalData.removeCache('search');
    },

    submit_search() {
      if (this.get_search === '') {
        uni.showToast({
          title: '请输入搜索内容',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      this.page = 1;
      this.new_list = [];
      this.get_index_list();
    },

    get_arr_dx(data) {
      const that = this;
      const e = getApp().globalData.getCache("userinfo");
      const params = {};
      params.token = e.token;
      params.openid = e.openid;
      params.arr = JSON.stringify(data);
      console.log(params);
      const b = getApp().globalData.api_root + 'User/set_arr_dx';
      http.POST(b, {
        params: params,
        success(res) {
          console.log(res);
          if (res.data.status === "success") {
            that.get_lishi = res.data.info;
          }
        },
        fail() {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
          });
        }
      });
    },

    get_index_list() {
      const b = getApp().globalData.api_root + 'User/get_search_list';
      const that = this;
      const e = getApp().globalData.getCache("userinfo");
      const params = {};
      params.token = e.token;
      params.openid = e.openid;
      params.page = this.page;
      params.search = this.get_search;
      const allMsg = this.new_list;

      http.POST(b, {
        params: params,
        success(res) {
          console.log(res);
          if (res.data.status === 'success') {
            if (res.data.info.length === 0) {
              that.di_msg = true;
            }
            that.new_list = allMsg.concat(res.data.info);
            that.set_list = true;
            that.territory = res.data.territory;
            that.user = res.data.user;
            that.is_search_yes = res.data.is_search_yes;
            that.user_search_yes = res.data.user_search_yes;

            let searchHistory = getApp().globalData.getCache("search") || [];
            if (!searchHistory.some(item => item.search === that.get_search)) {
              searchHistory.push({
                'search': that.get_search
              });
              getApp().globalData.setCache("search", searchHistory);
            }
          }
        },
        fail() {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
          });
        }
      });
    },

    previewImage(e) {
      this.show = false;
      const current = e.target.dataset.src;
      const id = e.target.dataset.id;
      uni.previewImage({
        current: current,
        urls: this.new_list[id]['image_part']
      });
    },

    play(e) {
      const that = this;
      const index = e.currentTarget.dataset.key;
      const nuw = this.new_list;
      const info = this.new_list[index];

      if (info['check_look'] == 0 && info['is_buy'] > 0) {
        innerAudioContext.stop();
        uni.navigateTo({
          url: `/yl_welore/pages/packageA/article/index?id=${info['id']}&type=${info['study_type']}`
        });
        return;
      }

      nuw.forEach(item => item.is_voice = false);

      this.$set(nuw[index], 'is_voice', true);
      this.new_list_index = index;

      innerAudioContext.src = e.currentTarget.dataset.vo;
      innerAudioContext.title = nuw[index]['study_title'] ? nuw[index]['study_title'] : '暂无标题';
      innerAudioContext.play();

      innerAudioContext.onTimeUpdate(() => {
        if (!that.new_list[index]) return;
        const duration = innerAudioContext.duration;
        const offset = innerAudioContext.currentTime;
        const currentTime = parseInt(innerAudioContext.currentTime);
        const min = "0" + parseInt(currentTime / 60);
        let sec = currentTime % 60;
        if (sec < 10) {
          sec = "0" + sec;
        }
        const starttime = min + ':' + sec;

        that.$set(that.new_list[index], 'starttime', starttime);
        that.$set(that.new_list[index], 'offset', offset);
      });

      innerAudioContext.onEnded(() => {
        if (!that.new_list[index]) return;
        that.$set(that.new_list[index], 'is_voice', false);
        that.$set(that.new_list[index], 'starttime', '00:00');
        that.$set(that.new_list[index], 'offset', 0);
        console.log("音乐播放结束");
      });
    },

    stop(e) {
      innerAudioContext.pause();
      const index = e.currentTarget.dataset.key;
      this.$set(this.new_list[index], 'is_voice', false);
    },

    sliderChange(e) {
      const index = e.currentTarget.dataset.key;
      const info = this.new_list[index];
      if (info['check_look'] == 0 && info['is_buy'] == 1) {
        innerAudioContext.stop();
        uni.navigateTo({
          url: `/yl_welore/pages/packageA/article/index?id=${info['id']}&type=${info['study_type']}`
        });
        return;
      }
      const offset = parseInt(e.detail.value);
      innerAudioContext.seek(offset);
      this.$set(this.new_list[index], 'is_voice', true);
      innerAudioContext.play();
    },

    rotate3d(key) {
      const animation_zan = uni.createAnimation({
        duration: 300,
        timingFunction: 'ease'
      });
      this.animation_zan = animation_zan;
      animation_zan.rotate3d(0, 1, 0, 180).step();
      this.$set(this.new_list[key], 'animationData_zan', animation_zan.export());

      setTimeout(() => {
        animation_zan.rotate3d(0, 1, 0, 0).step();
        this.$set(this.new_list[key], 'animationData_zan', animation_zan.export());
      }, 100);
    },

    _navback() {
      uni.navigateBack();
    }
  }
};
</script>
<style>
.page {
  background-color: #ffffff;
}

button::after {
  line-height: normal;
  font-size: 30rpx;
  width: 0;
  height: 0;
  top: 0;
  left: 0;
}

button {
  line-height: normal;
  display: block;
  padding-left: 0px;
  padding-right: 0px;
  background-color: rgba(255, 255, 255, 0);
  font-size: 30rpx;
  overflow: inherit;
}


/**index.wxss**/
.audiosBox {
  width: 92%;
  margin: auto;
  height: 130rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f6f7f7;
  border-radius: 10rpx;
}

/*按钮大小  */
.audioOpen {
  width: 70rpx;
  height: 70rpx;
  border: 2px solid #4c9dee;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.image2 {
  margin-left: 10%;
}

/*进度条长度  */
.slid {
  flex: 1;
  position: relative;
}

.slid view {
  display: flex;
  justify-content: space-between;
}

.slid view>text:nth-child(1) {
  color: #4c9dee;
  margin-left: 6rpx;
}

.slid view>text:nth-child(2) {
  margin-right: 6rpx;
}

slider {
  width: 520rpx;
  margin: 0;
  margin-left: 35rpx;
}

/*横向布局  */
.times {
  width: 100rpx;
  text-align: center;
  display: inline-block;
  font-size: 24rpx;
  color: #999999;
  margin-top: 5rpx;
}

.title view {
  text-indent: 2em;
}
</style>